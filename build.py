#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InfernoSpaceX Build Script
Secure compilation to executable with maximum protection
"""

import os
import sys
import shutil
import subprocess
import hashlib
import time
from pathlib import Path

class SecureBuilder:
    """Secure build system for InfernoSpaceX"""
    
    def __init__(self):
        self.project_name = "InfernoSpaceX"
        self.main_file = "app.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.output_name = "InfernoSpaceX.exe"
        
    def clean_build(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning previous builds...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, "__pycache__"]
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"   Removed {dir_name}")
        
        # Clean .pyc files
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith(".pyc"):
                    os.remove(os.path.join(root, file))
    
    def check_dependencies(self):
        """Check if all dependencies are installed"""
        print("📦 Checking dependencies...")
        
        required_packages = [
            "flask", "cryptography", "psutil", "pyinstaller"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package}")
        
        if missing_packages:
            print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
            print("Install them with: pip install -r requirements.txt")
            return False
        
        return True
    
    def create_spec_file(self):
        """Create PyInstaller spec file with security options"""
        print("📝 Creating secure spec file...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files
datas = []
datas += collect_data_files('flask')
datas += [('templates', 'templates')]
datas += [('static', 'static')]

# Collect hidden imports
hiddenimports = []
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('cryptography')
hiddenimports += collect_submodules('psutil')
hiddenimports += [
    'core.security.hardware_fingerprint',
    'core.security.anti_analysis',
    'core.crypto.hybrid_encryption',
    'core.database.fortress_db',
    'core.network.fortress_api',
    'core.monitoring.threat_detector'
]

# Analysis configuration
a = Analysis(
    ['{self.main_file}'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy',
        'PIL', 'cv2', 'tensorflow', 'torch', 'jupyter'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,  # We'll add our own encryption
    noarchive=False,
)

# Remove debugging symbols
a.binaries = [x for x in a.binaries if not x[0].endswith('.pdb')]

# PYZ archive with compression
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Executable configuration
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.output_name.replace(".exe", "")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # Strip symbols
    upx=True,   # Compress with UPX
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
        
        with open(f"{self.project_name}.spec", "w", encoding="utf-8") as f:
            f.write(spec_content)
        
        print("   ✅ Spec file created")
    
    def obfuscate_code(self):
        """Obfuscate Python code before compilation"""
        print("🔒 Obfuscating source code...")
        
        # Create obfuscated directory
        obf_dir = "obfuscated"
        if os.path.exists(obf_dir):
            shutil.rmtree(obf_dir)
        
        # Copy source files
        shutil.copytree(".", obf_dir, ignore=shutil.ignore_patterns(
            "build", "dist", "__pycache__", "*.pyc", "*.spec", 
            "obfuscated", ".git", "*.log"
        ))
        
        print("   ✅ Code prepared for obfuscation")
        return obf_dir
    
    def build_executable(self):
        """Build the executable with PyInstaller"""
        print("🔨 Building executable...")
        
        # PyInstaller command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "--onefile",
            "--windowed",
            "--optimize", "2",
            "--strip",
            f"--distpath={self.dist_dir}",
            f"--workpath={self.build_dir}",
            f"{self.project_name}.spec"
        ]
        
        print(f"   Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("   ✅ Build successful")
                return True
            else:
                print(f"   ❌ Build failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("   ❌ Build timed out")
            return False
        except Exception as e:
            print(f"   ❌ Build error: {e}")
            return False
    
    def post_process(self):
        """Post-process the executable"""
        print("⚙️ Post-processing executable...")
        
        exe_path = os.path.join(self.dist_dir, self.output_name)
        
        if not os.path.exists(exe_path):
            print(f"   ❌ Executable not found: {exe_path}")
            return False
        
        # Get file size and hash
        file_size = os.path.getsize(exe_path)
        
        with open(exe_path, "rb") as f:
            file_hash = hashlib.sha256(f.read()).hexdigest()
        
        print(f"   📁 Size: {file_size:,} bytes")
        print(f"   🔐 SHA256: {file_hash}")
        
        # Create info file
        info_content = f"""InfernoSpaceX Build Information
Build Date: {time.strftime('%Y-%m-%d %H:%M:%S')}
File Size: {file_size:,} bytes
SHA256: {file_hash}
Python Version: {sys.version}
Platform: {sys.platform}
"""
        
        with open(os.path.join(self.dist_dir, "build_info.txt"), "w") as f:
            f.write(info_content)
        
        print("   ✅ Post-processing complete")
        return True
    
    def create_installer(self):
        """Create installer package (optional)"""
        print("📦 Creating installer package...")
        
        # This would create an NSIS or Inno Setup installer
        # For now, just create a ZIP archive
        
        import zipfile
        
        zip_name = f"{self.project_name}_v1.0.0.zip"
        zip_path = os.path.join(self.dist_dir, zip_name)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add executable
            zipf.write(
                os.path.join(self.dist_dir, self.output_name),
                self.output_name
            )
            
            # Add build info
            zipf.write(
                os.path.join(self.dist_dir, "build_info.txt"),
                "build_info.txt"
            )
            
            # Add README
            readme_content = f"""# {self.project_name}

## Installation
1. Extract all files to a folder
2. Run {self.output_name}
3. The application will start automatically

## System Requirements
- Windows 10/11 (64-bit)
- 4GB RAM minimum
- 100MB free disk space

## Security Features
- Military-grade encryption
- Hardware binding
- Anti-debugging protection
- Secure authentication

## Support
For support, contact the development team.

Build Date: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            zipf.writestr("README.txt", readme_content)
        
        print(f"   ✅ Installer created: {zip_name}")
        return True
    
    def cleanup(self):
        """Clean up temporary files"""
        print("🧹 Cleaning up...")
        
        files_to_remove = [
            f"{self.project_name}.spec",
            "obfuscated"
        ]
        
        for item in files_to_remove:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
                print(f"   Removed {item}")
    
    def build(self):
        """Main build process"""
        print(f"🚀 Building {self.project_name}...")
        print("=" * 50)
        
        try:
            # Step 1: Clean
            self.clean_build()
            
            # Step 2: Check dependencies
            if not self.check_dependencies():
                return False
            
            # Step 3: Create spec file
            self.create_spec_file()
            
            # Step 4: Obfuscate (optional)
            # obf_dir = self.obfuscate_code()
            
            # Step 5: Build
            if not self.build_executable():
                return False
            
            # Step 6: Post-process
            if not self.post_process():
                return False
            
            # Step 7: Create installer
            self.create_installer()
            
            # Step 8: Cleanup
            self.cleanup()
            
            print("=" * 50)
            print("🎉 Build completed successfully!")
            print(f"📁 Output: {os.path.join(self.dist_dir, self.output_name)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Build failed: {e}")
            return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("""
InfernoSpaceX Build Script

Usage:
    python build.py          - Build the application
    python build.py --help   - Show this help

The script will:
1. Clean previous builds
2. Check dependencies
3. Create PyInstaller spec file
4. Build executable with security features
5. Post-process and create installer
6. Clean up temporary files
        """)
        return
    
    builder = SecureBuilder()
    success = builder.build()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
