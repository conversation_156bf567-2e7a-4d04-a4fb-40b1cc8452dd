/* Midnight Dashboard - Топовый полуночный дизайн */

/* Переменные для midnight темы */
:root {
    /* Midnight Colors */
    --midnight-bg: #0a0a0f;
    --midnight-surface: #1a1a2e;
    --midnight-card: #16213e;
    --midnight-accent: #0f3460;
    
    /* Neon Colors */
    --neon-purple: #8b5cf6;
    --neon-blue: #3b82f6;
    --neon-cyan: #06b6d4;
    --neon-pink: #ec4899;
    --neon-green: #10b981;
    --neon-orange: #f59e0b;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-dark: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    
    /* Glass effect enhanced */
    --glass-bg-midnight: rgba(255, 255, 255, 0.03);
    --glass-border-midnight: rgba(255, 255, 255, 0.08);
    --glass-shadow-midnight: 0 8px 32px rgba(0, 0, 0, 0.6);
    
    /* Glow effects */
    --glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
    --glow-blue: 0 0 20px rgba(59, 130, 246, 0.3);
    --glow-cyan: 0 0 20px rgba(6, 182, 212, 0.3);
    --glow-pink: 0 0 20px rgba(236, 72, 153, 0.3);
}

/* Body override для midnight темы */
body {
    background: var(--gradient-dark);
    overflow-x: hidden;
}

/* Particle system background */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--neon-cyan);
    border-radius: 50%;
    opacity: 0.6;
    animation: float-particle 8s linear infinite;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 0;
    gap: 0;
    background: transparent;
    position: relative;
    overflow-x: hidden;
}

/* Enhanced Header */
.dashboard-header {
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 0 0 24px 24px;
    box-shadow: var(--glass-shadow-midnight);
    padding: 1.5rem 2rem;
    margin: 0;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-accent);
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.logo-icon {
    font-size: 2.5rem;
    filter: drop-shadow(var(--glow-blue));
    animation: logo-pulse 3s ease-in-out infinite;
}

@keyframes logo-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.header-title {
    font-size: 2rem;
    font-weight: 600;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
    text-shadow: 0 0 30px rgba(79, 172, 254, 0.5);
}

.header-subtitle {
    font-size: 1rem;
    color: var(--neon-cyan);
    font-weight: 300;
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

/* User Info Enhanced */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    background: var(--glass-bg-midnight);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--neon-purple);
    box-shadow: var(--glow-purple);
    transform: translateY(-2px);
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border: 2px solid var(--neon-purple);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    box-shadow: var(--glow-purple);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 0 25px rgba(139, 92, 246, 0.6);
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Welcome Section Enhanced */
.welcome-card {
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow-midnight);
    transition: all 0.4s ease;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, var(--neon-purple), transparent);
    animation: rotate-border 4s linear infinite;
    opacity: 0.3;
}

.welcome-card::after {
    content: '';
    position: absolute;
    inset: 2px;
    background: var(--midnight-bg);
    border-radius: 22px;
    z-index: -1;
}

@keyframes rotate-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.welcome-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
}

.welcome-icon {
    font-size: 5rem;
    filter: drop-shadow(var(--glow-cyan));
    animation: welcome-float 3s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes welcome-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: 0 0 30px rgba(79, 172, 254, 0.5);
}

.welcome-text {
    font-size: 1.2rem;
    color: var(--neon-cyan);
    font-weight: 300;
    text-shadow: 0 0 15px rgba(6, 182, 212, 0.4);
}

/* Stats Grid Enhanced */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    cursor: pointer;
    box-shadow: var(--glass-shadow-midnight);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
    border-color: var(--neon-blue);
}

.stat-card:nth-child(1):hover {
    box-shadow: var(--glow-green), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.stat-card:nth-child(2):hover {
    box-shadow: var(--glow-blue), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.stat-card:nth-child(3):hover {
    box-shadow: var(--glow-pink), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.stat-card:nth-child(4):hover {
    box-shadow: var(--glow-purple), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 10px currentColor);
    animation: stat-pulse 2s ease-in-out infinite;
}

@keyframes stat-pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.05); opacity: 1; }
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    animation: number-glow 3s ease-in-out infinite;
}

@keyframes number-glow {
    0%, 100% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.3); }
    50% { text-shadow: 0 0 30px rgba(255, 255, 255, 0.6); }
}

.stat-label {
    font-size: 1rem;
    color: var(--neon-cyan);
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.4);
}

/* Actions Grid Enhanced */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s ease;
    text-decoration: none;
    color: inherit;
    box-shadow: var(--glass-shadow-midnight);
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.action-card:hover::before {
    left: 100%;
}

.action-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
    border-color: var(--neon-purple);
}

.action-card:nth-child(1):hover {
    border-color: var(--neon-green);
    box-shadow: var(--glow-green), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.action-card:nth-child(2):hover {
    border-color: var(--neon-blue);
    box-shadow: var(--glow-blue), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.action-card:nth-child(3):hover {
    border-color: var(--neon-orange);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.action-card:nth-child(4):hover {
    border-color: var(--neon-pink);
    box-shadow: var(--glow-pink), 0 25px 50px rgba(0, 0, 0, 0.8);
}

.action-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 10px currentColor);
    animation: action-float 4s ease-in-out infinite;
}

@keyframes action-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-5px) rotate(1deg); }
    75% { transform: translateY(5px) rotate(-1deg); }
}

.action-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.action-description {
    font-size: 1rem;
    color: var(--neon-cyan);
    font-weight: 300;
    line-height: 1.5;
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

/* Status Indicator Enhanced */
.status-indicator {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 50px;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1000;
    box-shadow: var(--glass-shadow-midnight);
    transition: all 0.3s ease;
}

.status-indicator:hover {
    transform: translateY(-3px);
    box-shadow: var(--glow-green), 0 15px 30px rgba(0, 0, 0, 0.6);
}

.status-dot {
    width: 15px;
    height: 15px;
    background: var(--neon-green);
    border-radius: 50%;
    box-shadow: 0 0 15px var(--neon-green);
    animation: status-pulse-enhanced 2s ease-in-out infinite;
}

@keyframes status-pulse-enhanced {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 15px var(--neon-green);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
        box-shadow: 0 0 25px var(--neon-green);
    }
}

.status-text {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Modal Enhanced */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 2rem;
    animation: modal-fade-in 0.3s ease;
}

@keyframes modal-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--glass-bg-midnight);
    backdrop-filter: blur(30px);
    border: 1px solid var(--glass-border-midnight);
    border-radius: 24px;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.8);
    overflow: hidden;
    animation: modal-slide-up 0.3s ease;
}

@keyframes modal-slide-up {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--glass-border-midnight);
    background: linear-gradient(135deg, var(--neon-purple), var(--neon-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.modal-body {
    padding: 2rem;
    color: var(--neon-cyan);
    line-height: 1.6;
    font-size: 1.1rem;
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    border-top: 1px solid var(--glass-border-midnight);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Button Enhanced */
.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--glow-purple);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
}

.btn-secondary {
    background: transparent;
    color: var(--neon-cyan);
    border: 2px solid var(--neon-cyan);
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.3);
}

.btn-secondary:hover {
    background: var(--neon-cyan);
    color: var(--midnight-bg);
    transform: translateY(-2px);
    box-shadow: 0 0 25px rgba(6, 182, 212, 0.6);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--glass-border-midnight);
    padding: 0.5rem 1rem;
    border-radius: 12px;
}

.btn-ghost:hover {
    background: var(--glass-bg-midnight);
    color: var(--text-primary);
    border-color: var(--neon-purple);
    box-shadow: var(--glow-purple);
}

/* Logout Icon Enhanced */
.logout-icon {
    font-size: 1.2rem;
    filter: drop-shadow(0 0 5px currentColor);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 0;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
        border-radius: 0 0 20px 20px;
    }

    .header-left,
    .header-right {
        justify-content: center;
    }

    .header-title {
        font-size: 1.8rem;
    }

    .dashboard-main {
        padding: 1.5rem;
        gap: 1.5rem;
    }

    .welcome-card {
        padding: 2rem;
    }

    .welcome-icon {
        font-size: 4rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .actions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-card {
        padding: 1.5rem;
    }

    .status-indicator {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        justify-content: center;
    }

    .particle {
        display: none; /* Hide particles on mobile for performance */
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .header-subtitle {
        font-size: 0.9rem;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .welcome-card {
        padding: 1.5rem;
    }

    .welcome-icon {
        font-size: 3.5rem;
    }

    .welcome-title {
        font-size: 1.8rem;
    }

    .welcome-text {
        font-size: 1rem;
    }

    .stat-card,
    .action-card {
        padding: 1.25rem;
    }

    .stat-icon,
    .action-icon {
        font-size: 2.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .action-title {
        font-size: 1.2rem;
    }

    .user-info {
        padding: 0.5rem 1rem;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
    }

    .modal {
        padding: 1rem;
    }

    .modal-content {
        border-radius: 20px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1.5rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--midnight-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--neon-purple);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--neon-cyan);
    box-shadow: 0 0 15px rgba(6, 182, 212, 0.7);
}

/* Selection Styling */
::selection {
    background: var(--neon-purple);
    color: white;
    text-shadow: none;
}

::-moz-selection {
    background: var(--neon-purple);
    color: white;
    text-shadow: none;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--neon-cyan);
    outline-offset: 2px;
    border-radius: 4px;
}

/* Advanced Visual Effects */
.dashboard-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Glitch Effect for Title */
@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

.header-title:hover {
    animation: glitch 0.3s ease-in-out;
}

/* Holographic Effect */
.holographic {
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    background-size: 200% 200%;
    animation: holographic-shine 3s ease-in-out infinite;
}

@keyframes holographic-shine {
    0% { background-position: -200% -200%; }
    50% { background-position: 200% 200%; }
    100% { background-position: -200% -200%; }
}

/* Neon Border Animation */
.neon-border {
    position: relative;
    overflow: hidden;
}

.neon-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        var(--neon-purple),
        var(--neon-blue),
        var(--neon-cyan),
        var(--neon-pink));
    border-radius: inherit;
    z-index: -1;
    animation: neon-rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.neon-border:hover::before {
    opacity: 1;
}

@keyframes neon-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Matrix Rain Effect */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

.matrix-char {
    position: absolute;
    color: var(--neon-green);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    animation: matrix-fall 3s linear infinite;
}

@keyframes matrix-fall {
    0% {
        transform: translateY(-100vh);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Cyberpunk Grid */
.cyber-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(139, 92, 246, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(139, 92, 246, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -2;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Enhanced Hover States */
.stat-card:hover .stat-icon {
    animation: icon-bounce 0.6s ease-in-out;
}

@keyframes icon-bounce {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

.action-card:hover .action-icon {
    animation: icon-spin 0.8s ease-in-out;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Loading Animation Enhancement */
.loading-pulse {
    animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
}

/* Performance Optimizations */
.dashboard-container * {
    will-change: transform;
}

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg-midnight: rgba(255, 255, 255, 0.02);
        --glass-border-midnight: rgba(255, 255, 255, 0.05);
    }
}
