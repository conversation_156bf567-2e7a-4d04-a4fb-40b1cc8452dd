/* Simple Dashboard - Только 3 вкладки */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

/* Main Container */
.simple-dashboard {
    display: flex;
    height: 100vh;
}

/* Sidebar - Простой */
.sidebar {
    width: 250px;
    background: #2d2d2d;
    border-right: 1px solid #404040;
    display: flex;
    flex-direction: column;
}

/* Header в сайдбаре */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #404040;
    text-align: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 5px;
}

.user-info {
    font-size: 14px;
    color: #888;
}

/* Навигация - 3 кнопки */
.nav-tabs {
    flex: 1;
    padding: 20px 0;
}

.tab-button {
    display: block;
    width: 100%;
    padding: 15px 20px;
    background: none;
    border: none;
    color: #ccc;
    font-size: 16px;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.tab-button:hover {
    background: #3d3d3d;
    color: #fff;
}

.tab-button.active {
    background: #ff6b35;
    color: #fff;
    border-left-color: #ff8c42;
}

.tab-button i {
    margin-right: 10px;
    width: 20px;
}

/* Logout кнопка */
.logout-section {
    padding: 20px;
    border-top: 1px solid #404040;
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background: #dc3545;
    border: none;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.logout-btn:hover {
    background: #c82333;
}

/* Main Content */
.main-content {
    flex: 1;
    background: #1a1a1a;
    overflow-y: auto;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 30px;
    height: 100%;
}

.tab-content.active {
    display: block;
}

/* Page Headers */
.page-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #ff6b35;
}

.page-subtitle {
    font-size: 16px;
    color: #888;
    margin-bottom: 30px;
}

/* Cards */
.card {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #fff;
}

/* Profile Styles */
.profile-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #404040;
}

.info-label {
    color: #888;
}

.info-value {
    color: #fff;
    font-weight: 500;
}

/* Chat Styles */
.chat-container {
    height: 500px;
    display: flex;
    flex-direction: column;
    background: #2d2d2d;
    border-radius: 8px;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #1a1a1a;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    background: #2d2d2d;
    border-radius: 8px;
    border-left: 3px solid #ff6b35;
}

.message-header {
    font-size: 12px;
    color: #888;
    margin-bottom: 5px;
}

.message-text {
    color: #fff;
}

.chat-input-area {
    padding: 15px;
    background: #2d2d2d;
    border-top: 1px solid #404040;
    display: flex;
    gap: 10px;
}

.chat-input {
    flex: 1;
    padding: 10px 15px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
}

.chat-input:focus {
    outline: none;
    border-color: #ff6b35;
}

.send-btn {
    padding: 10px 20px;
    background: #ff6b35;
    border: none;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.send-btn:hover {
    background: #ff8c42;
}

/* Пробив Styles */
.lookup-tools {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.tool-section {
    background: #2d2d2d;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #404040;
}

.tool-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #ff6b35;
}

.input-group {
    margin-bottom: 15px;
}

.input-label {
    display: block;
    margin-bottom: 5px;
    color: #ccc;
    font-size: 14px;
}

.tool-input {
    width: 100%;
    padding: 10px 15px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
}

.tool-input:focus {
    outline: none;
    border-color: #ff6b35;
}

.tool-btn {
    width: 100%;
    padding: 12px;
    background: #ff6b35;
    border: none;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.tool-btn:hover {
    background: #ff8c42;
}

.results-area {
    margin-top: 15px;
    padding: 15px;
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 5px;
    min-height: 100px;
    color: #ccc;
    font-family: monospace;
    font-size: 13px;
}

/* Light Theme */
.theme-light {
    background: #ffffff;
    color: #333333;
}

.theme-light .sidebar {
    background: #f8f9fa;
    border-right-color: #dee2e6;
}

.theme-light .sidebar-header {
    border-bottom-color: #dee2e6;
}

.theme-light .tab-button {
    color: #666;
}

.theme-light .tab-button:hover {
    background: #e9ecef;
    color: #333;
}

.theme-light .main-content {
    background: #ffffff;
}

.theme-light .card,
.theme-light .tool-section {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.theme-light .chat-messages {
    background: #ffffff;
}

.theme-light .message {
    background: #f8f9fa;
}

.theme-light .chat-input,
.theme-light .tool-input {
    background: #ffffff;
    border-color: #dee2e6;
    color: #333;
}

.theme-light .results-area {
    background: #ffffff;
    border-color: #dee2e6;
    color: #666;
}

/* Responsive */
@media (max-width: 768px) {
    .simple-dashboard {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .nav-tabs {
        display: flex;
        padding: 10px;
    }
    
    .tab-button {
        flex: 1;
        text-align: center;
        padding: 10px;
    }
    
    .profile-info,
    .lookup-tools {
        grid-template-columns: 1fr;
    }
}
