/* Dashboard Styles */

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-xl);
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    position: relative;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-info {
    display: flex;
    flex-direction: column;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: 0.5px;
}

.header-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 300;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-name {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 500;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

/* Welcome Section */
.welcome-section {
    width: 100%;
}

.welcome-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    justify-content: center;
}

.welcome-icon {
    font-size: 4rem;
    animation: pulse 2s infinite;
}

.welcome-content {
    flex: 1;
    max-width: 400px;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 0.5px;
}

.welcome-text {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 300;
}

/* Stats Section */
.stats-section {
    width: 100%;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.9;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-family: 'Courier New', monospace;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 300;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Actions Section */
.actions-section {
    width: 100%;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: left;
}

.action-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.action-card:active {
    transform: translateY(-2px);
}

.action-icon {
    font-size: 2.5rem;
    opacity: 0.9;
    flex-shrink: 0;
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.action-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 300;
    line-height: 1.4;
}

/* Status Indicator */
.status-indicator {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    z-index: 1000;
}

.status-dot {
    width: 12px;
    height: 12px;
    background: #10B981;
    border-radius: 50%;
    animation: status-pulse 2s infinite;
}

@keyframes status-pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

.status-text {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(var(--blur-md));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
}

.modal-content {
    width: 100%;
    max-width: 400px;
    padding: 0;
}

.modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.modal-body {
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    line-height: 1.6;
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
    border-top: 1px solid var(--glass-border);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .header-left,
    .header-right {
        justify-content: center;
    }
    
    .welcome-card {
        flex-direction: column;
        text-align: center;
        padding: var(--spacing-xl);
    }
    
    .welcome-title {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .action-card {
        padding: var(--spacing-lg);
    }
    
    .status-indicator {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
        left: var(--spacing-lg);
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: var(--spacing-lg);
    }
    
    .header-title {
        font-size: 1.2rem;
    }
    
    .welcome-icon {
        font-size: 3rem;
    }
    
    .welcome-title {
        font-size: 1.3rem;
    }
    
    .stat-card,
    .action-card {
        padding: var(--spacing-lg);
    }
    
    .stat-icon,
    .action-icon {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .action-title {
        font-size: 1.1rem;
    }
}
