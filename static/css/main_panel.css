/* Main Panel - Clean & Modern Design */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #06b6d4;
    
    /* Backgrounds */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    
    /* Text */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    
    /* Borders */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --bg-card: #1e293b;
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
        --border-color: #334155;
        --border-hover: #475569;
    }
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.logo-icon {
    font-size: 1.5rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.username {
    font-weight: 500;
    color: var(--text-primary);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.logout-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
}

.tab-btn {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    position: relative;
    flex: 1;
    justify-content: center;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.chat-indicator {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: var(--transition);
}

.chat-indicator.active {
    opacity: 1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Tab Content */
.tab-content {
    min-height: 600px;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.panel-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.panel-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.panel-header p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

/* Cards */
.profile-grid,
.osint-tools {
    display: grid;
    gap: var(--spacing-xl);
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.profile-card,
.tool-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.profile-card:hover,
.tool-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-xl);
}

/* Profile specific */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.label {
    color: var(--text-secondary);
    font-weight: 500;
}

.value {
    color: var(--text-primary);
    font-weight: 600;
}

.status.online {
    color: var(--success);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    text-align: center;
}

.stat-item {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Input Groups */
.input-group {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.tool-input,
.chat-input-field {
    flex: 1;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.tool-input:focus,
.chat-input-field:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.tool-btn,
.send-btn {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.tool-btn:hover,
.send-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.tool-btn:disabled,
.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Result Areas */
.result-area {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    min-height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--text-secondary);
    white-space: pre-wrap;
    overflow-y: auto;
}

.result-area:empty::before {
    content: 'Результаты анализа появятся здесь...';
    color: var(--text-muted);
    font-style: italic;
}

/* Chat */
.chat-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.chat-message {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.chat-message.own {
    background: var(--primary);
    color: white;
    margin-left: auto;
    max-width: 70%;
}

.chat-message.system {
    background: var(--info);
    color: white;
    text-align: center;
    font-style: italic;
}

.message-avatar {
    width: 32px;
    height: 32px;
    background: var(--secondary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.message-username {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.message-role {
    background: var(--primary);
    color: white;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.message-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-left: auto;
}

.message-text {
    color: var(--text-primary);
    word-wrap: break-word;
}

.chat-input-area {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
}

.char-counter {
    text-align: right;
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: var(--spacing-sm);
}

/* Loading & Modals */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.loading-overlay.active {
    display: flex;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    overflow: hidden;
    animation: modalSlide 0.3s ease-out;
}

@keyframes modalSlide {
    from { opacity: 0; transform: scale(0.9) translateY(-20px); }
    to { opacity: 1; transform: scale(1) translateY(0); }
}

.modal-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-content {
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.modal-actions {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: toastSlide 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

@keyframes toastSlide {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

.toast::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--info);
}

.toast.success::before { background: var(--success); }
.toast.error::before { background: var(--error); }
.toast.warning::before { background: var(--warning); }

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .nav-tabs {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
    }

    .profile-grid,
    .osint-tools {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .header {
        padding: var(--spacing-md);
    }

    .header-right {
        gap: var(--spacing-md);
    }

    .username {
        display: none;
    }

    .chat-container {
        height: 500px;
    }

    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .toast {
        min-width: auto;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-hover);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary);
}

/* Focus States */
*:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Selection */
::selection {
    background: var(--primary);
    color: white;
}
