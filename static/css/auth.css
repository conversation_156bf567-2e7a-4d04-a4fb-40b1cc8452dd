/* Authentication Pages Styles */

/* Ultra Modern Auth Container */
.auth-container {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    position: relative;
    perspective: 1000px;
}

.auth-card {
    padding: 3rem;
    position: relative;
    border-radius: 28px;
    background: rgba(255, 255, 255, 0.06);
    backdrop-filter: blur(60px);
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.5),
        0 12px 40px rgba(0, 0, 0, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.2),
        inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    border: 2px solid;
    border-image: linear-gradient(135deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%) 1;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(102, 126, 234, 0.1) 90deg,
        rgba(118, 75, 162, 0.1) 180deg,
        transparent 270deg,
        transparent 360deg
    );
    animation: rotate 8s linear infinite;
    z-index: -1;
}

.auth-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 28px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.03) 50%,
        rgba(255, 255, 255, 0.01) 100%);
    z-index: -1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.auth-card:hover {
    transform: translateY(-12px) rotateX(3deg) scale(1.02);
    box-shadow:
        0 40px 100px rgba(0, 0, 0, 0.6),
        0 20px 60px rgba(0, 0, 0, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

/* Back Button */
.back-btn {
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.back-icon {
    font-size: 1.5rem;
    font-weight: bold;
}

/* Ultra Modern Logo Section */
.logo-container {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.logo-icon {
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: logoFloat 3s ease-in-out infinite, gradientShift 4s ease-in-out infinite;
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.4));
    position: relative;
}

.logo-icon::before {
    content: '🔥';
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shine 2s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    50% { transform: translateY(-12px) rotate(3deg) scale(1.05); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes shine {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Ultra Modern Auth Titles */
.auth-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 30%, #e2e8f0 70%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    letter-spacing: -0.03em;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: titleGlow 3s ease-in-out infinite, gradientShift 4s ease-in-out infinite;
    position: relative;
}

.auth-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: lineGlow 2s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3)); }
    50% { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
}

@keyframes lineGlow {
    0%, 100% { box-shadow: 0 0 10px rgba(102, 126, 234, 0.5); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
}

.auth-subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 0.02em;
    line-height: 1.5;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.logo-text {
    font-size: 2rem;
    font-weight: 300;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 1px;
}

.logo-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 300;
}

/* Modern Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

.form-label {
    font-size: 0.95rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    letter-spacing: 0.01em;
    transition: all 0.3s ease;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    padding-left: 3.5rem;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    color: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
    font-weight: 400;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.6);
    background: rgba(255, 255, 255, 0.12);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 300;
}

.input-icon {
    position: absolute;
    left: 1.25rem;
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.6);
    pointer-events: none;
    z-index: 2;
    transition: all 0.3s ease;
}

.form-group:focus-within .input-icon {
    color: rgba(102, 126, 234, 0.8);
    transform: scale(1.1);
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.toggle-icon {
    font-size: 1.1rem;
}

.input-hint {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Form Options - Компактная версия */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--spacing-sm) 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary);
    border-color: var(--primary);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.forgot-password {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary);
}

.terms-link {
    color: var(--primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.terms-link:hover {
    color: var(--primary-dark);
}

/* Divider */
.divider {
    display: flex;
    align-items: center;
    margin: var(--spacing-lg) 0;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--glass-border);
}

.divider span {
    padding: 0 var(--spacing-md);
}

/* Test Info - Компактная версия */
.test-info {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.info-icon {
    font-size: 1.5rem;
    color: var(--info);
}

.info-content {
    flex: 1;
}

.info-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.info-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(var(--blur-md));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
}

.modal-content {
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 0;
}

.modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    line-height: 1.6;
}

.modal-body ul {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-xl);
}

.modal-body li {
    margin-bottom: var(--spacing-sm);
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
    border-top: 1px solid var(--glass-border);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
}

/* Button Loading States */
.btn .btn-text {
    transition: opacity var(--transition-fast);
}

.btn.loading .btn-text {
    opacity: 0;
}

.btn.loading .btn-loader {
    display: block;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-card {
        padding: var(--spacing-xl);
    }
    
    .logo-icon {
        font-size: 3rem;
    }
    
    .logo-text {
        font-size: 1.5rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
    
    .modal-content {
        margin: var(--spacing-md);
        max-width: none;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: var(--spacing-lg);
        padding-right: var(--spacing-lg);
    }
}

/* Modern Compact Design Enhancements */
.auth-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0.8;
}

.auth-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* Compact Form Styling */
.form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 4px;
}

.form-input {
    padding: 12px 16px;
    font-size: 0.95rem;
    border-radius: 8px;
}

/* Modern Button Styles */
.btn-primary {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 24px rgba(102, 126, 234, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.02em;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 16px 40px rgba(102, 126, 234, 0.4),
        0 8px 24px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
}
