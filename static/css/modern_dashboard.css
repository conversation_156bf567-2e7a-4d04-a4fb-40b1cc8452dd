/* Modern Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow: hidden; /* Remove all scrollbars */
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
}

/* Command Center Layout - FULL SCREEN */
.command-center {
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Top Navigation - FULL WIDTH */
.top-nav {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    box-sizing: border-box;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.brand-icon {
    font-size: 2.5rem;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.brand-text h1 {
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.brand-text span {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 1rem;
}

.user-role {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Main Content Layout - FULL SCREEN NO SCROLL */
.main-content {
    display: flex;
    flex: 1;
    height: calc(100vh - 80px);
    width: 100%;
    max-width: none;
    overflow: hidden;
}

/* Sidebar - WIDER */
.sidebar {
    width: 320px;
    min-width: 320px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    flex-shrink: 0;
}

.tab-navigation {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
}

.tab-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    font-weight: 500;
    text-align: left;
}

.tab-btn i {
    font-size: 1.2rem;
    width: 20px;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    transform: translateX(5px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Content Area - FULL WIDTH NO EXTERNAL SCROLL */
.content-area {
    flex: 1;
    padding: 2rem 3rem;
    overflow-y: auto;
    overflow-x: hidden;
    width: calc(100vw - 320px);
    max-width: none;
    height: 100%;

    /* Custom scrollbar */
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.5) transparent;
}

.content-area::-webkit-scrollbar {
    width: 6px;
}

.content-area::-webkit-scrollbar-track {
    background: transparent;
}

.content-area::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.5);
    border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.7);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-header {
    margin-bottom: 2rem;
}

.content-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.content-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.1rem;
}

/* Profile Grid - FULL SCREEN */
.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    max-width: none;
}

.profile-card, .tool-card, .security-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-card:hover, .tool-card:hover, .security-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.card-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-content {
    padding: 1.5rem;
}

.info-item, .setting-item, .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item:last-child, .setting-item:last-child, .stat-item:last-child {
    border-bottom: none;
}

.info-item label, .setting-item label, .stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Enhanced Profile Styles */
.profile-avatar-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 800;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.profile-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(255,255,255,0.2) 90deg,
        transparent 180deg
    );
    animation: rotate 3s linear infinite;
}

.profile-basic-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.role-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.role-badge-large {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    display: inline-block;
}

/* Role-specific colors */
.role-user { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); }
.role-premium { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
.role-subscription { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
.role-admin { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

.profile-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-box {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-box:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.stat-box i {
    font-size: 1.5rem;
    color: #667eea;
    width: 24px;
    text-align: center;
}

.stat-box div {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-weight: 600;
    color: white;
}

.status-secure {
    color: #10b981;
    font-weight: 700;
}

.status-online {
    color: #10b981;
    font-weight: 600;
}

.security-level {
    color: #10b981;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Setting Select */
.setting-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Probiv Tools - FULL SCREEN */
.probiv-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 3rem;
    max-width: none;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tool-input, .chat-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 1rem;
}

.tool-input:focus, .chat-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

.full-width {
    width: 100%;
    justify-content: center;
    margin-bottom: 0.75rem;
}

.results-area {
    min-height: 100px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #10b981;
}

/* Chat Container - FULL SCREEN */
.chat-container {
    height: 600px;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    overflow: hidden;
    max-width: none;
    width: 100%;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    /* Custom scrollbar for chat */
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.message {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.message.system .message-content {
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message-time {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    text-align: right;
}

.chat-input-area {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Security Grid - FULL SCREEN */
.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
    max-width: none;
}

.stat-value {
    font-weight: 700;
    font-size: 1.1rem;
}

/* Professional Light Theme */
.theme-light {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    color: #0f172a;
}

.theme-light .top-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-light .sidebar {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(0, 0, 0, 0.08);
}

.theme-light .tab-btn {
    color: #475569;
}

.theme-light .tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #334155;
}

.theme-light .tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.theme-light .profile-card,
.theme-light .tool-card,
.theme-light .security-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.theme-light .profile-card:hover,
.theme-light .tool-card:hover,
.theme-light .security-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(102, 126, 234, 0.2);
}

.theme-light .brand-text h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.theme-light .user-name,
.theme-light .content-header h2 {
    color: #0f172a;
}

.theme-light .user-role {
    color: #64748b;
}

.theme-light .content-area::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.4);
}

.theme-light .content-area::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.6);
}

.theme-light .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
}

.theme-light .chat-container {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.theme-light .chat-input-area {
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.theme-light .tool-input,
.theme-light .chat-input {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    color: #0f172a;
}

.theme-light .tool-input:focus,
.theme-light .chat-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-light .setting-select {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.1);
    color: #0f172a;
}

.theme-light .info-item label,
.theme-light .setting-item label,
.theme-light .stat-label {
    color: #475569;
}

.theme-light .stat-value {
    color: #0f172a;
}

.theme-light .card-header {
    background: rgba(102, 126, 234, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.theme-light .card-header h3 {
    color: #334155;
}

.theme-light .stat-box {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.theme-light .stat-box:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-light .results-area {
    background: rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #0f172a;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .tab-navigation {
        flex-direction: row;
        overflow-x: auto;
        padding: 1rem;
    }

    .tab-btn {
        min-width: 120px;
        justify-content: center;
    }

    .profile-grid, .probiv-tools, .security-grid {
        grid-template-columns: 1fr;
    }
}
