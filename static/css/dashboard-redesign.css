/* InfernoSpaceX Dashboard - Professional Redesign */

/* CSS Custom Properties for Consistency */
:root {
    /* Professional Color Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    
    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Theme Variables */
.theme-dark {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --text-primary: #ffffff;
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
    --border-color: var(--gray-700);
    --card-bg: rgba(255, 255, 255, 0.05);
    --card-border: rgba(255, 255, 255, 0.1);
}

/* Light Theme Variables */
.theme-light {
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-muted: var(--gray-500);
    --border-color: var(--gray-200);
    --card-bg: #ffffff;
    --card-border: var(--gray-200);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-primary);
    transition: all 0.2s ease;
}

/* Professional Dashboard Layout */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Top Navigation - Clean and Professional */
.top-nav {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--space-4) var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    color: white;
    box-shadow: var(--shadow-md);
}

.brand-text h1 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.brand-text span {
    font-size: var(--text-sm);
    color: var(--text-muted);
    font-weight: 500;
}

/* User Navigation */
.nav-user {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    font-weight: 600;
    color: white;
    box-shadow: var(--shadow-md);
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    font-size: var(--text-sm);
    color: var(--text-primary);
}

.user-role {
    font-size: var(--text-xs);
    color: var(--text-muted);
}

.logout-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--text-sm);
}

.logout-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex: 1;
    min-height: 0;
}

/* Sidebar - Clean and Minimal */
.sidebar {
    width: 280px;
    background: var(--card-bg);
    border-right: 1px solid var(--border-color);
    padding: var(--space-6) 0;
    overflow-y: auto;
}

.tab-navigation {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    padding: 0 var(--space-4);
}

.tab-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-sm);
    font-weight: 500;
    text-align: left;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-md);
}

.tab-btn i {
    font-size: var(--text-base);
    width: 20px;
    text-align: center;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--space-6);
    overflow-y: auto;
    background: var(--bg-secondary);
}

.content-header {
    margin-bottom: var(--space-8);
}

.content-header h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.content-header p {
    color: var(--text-muted);
    font-size: var(--text-base);
}

/* Cards - Professional Design */
.card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.card-header {
    padding: var(--space-5);
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-content {
    padding: var(--space-5);
}

/* Grid Layouts */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

@media (max-width: 768px) {
    .grid-cols-2,
    .grid-cols-3 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
}

/* Form Elements */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: var(--text-sm);
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Buttons */
.btn {
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
}

.btn-primary {
    background: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-600);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Profile Components */
.profile-section {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-color);
}

.profile-avatar-large {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    font-weight: 700;
    color: white;
    box-shadow: var(--shadow-lg);
}

.profile-info h4 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
}

.role-badge {
    padding: var(--space-1) var(--space-3);
    background: var(--primary-500);
    color: white;
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: 600;
    display: inline-block;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
}

.info-label {
    font-size: var(--text-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.info-value {
    font-size: var(--text-sm);
    color: var(--text-primary);
    font-weight: 500;
}

/* Statistics Components */
.stats-grid {
    display: grid;
    gap: var(--space-4);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.stat-item:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-500);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-base);
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.stat-label {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: 500;
}

/* Chat Components */
.chat-container {
    height: 500px;
    display: flex;
    flex-direction: column;
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: var(--space-4);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.chat-message {
    display: flex;
    gap: var(--space-3);
    animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    font-weight: 600;
    color: white;
    flex-shrink: 0;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
}

.message-username {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.message-role {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
    background: var(--primary-500);
    color: white;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.message-time {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-left: auto;
}

.message-text {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    word-wrap: break-word;
}

.chat-input-area {
    padding: var(--space-4);
    border-top: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.chat-input-group {
    display: flex;
    gap: var(--space-2);
}

.chat-input-group .form-input {
    flex: 1;
}

/* Results Components */
.results-area {
    margin-top: var(--space-4);
    padding: var(--space-4);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    min-height: 100px;
}

.loading, .error {
    text-align: center;
    padding: var(--space-4);
    border-radius: var(--radius-md);
}

.loading {
    color: var(--primary-500);
    background: rgba(59, 130, 246, 0.1);
}

.error {
    color: var(--error-500);
    background: rgba(239, 68, 68, 0.1);
}

.ip-analysis-result,
.user-search-result {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.result-header {
    padding: var(--space-3) var(--space-4);
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.result-header h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.result-content {
    padding: var(--space-4);
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--border-color);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-size: var(--text-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.result-value {
    font-size: var(--text-sm);
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
}

/* Utility Classes */
.full-width {
    width: 100%;
    margin-bottom: var(--space-3);
}

.full-width:last-child {
    margin-bottom: 0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 240px;
    }

    .grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .tab-navigation {
        flex-direction: row;
        overflow-x: auto;
        padding: var(--space-4);
    }

    .tab-btn {
        min-width: 120px;
        justify-content: center;
    }

    .content-area {
        padding: var(--space-4);
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
    }

    .chat-container {
        height: 400px;
    }
}
