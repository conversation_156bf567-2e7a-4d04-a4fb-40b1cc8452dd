/* InfernoSpaceX - Premium Dashboard Design */

/* Modern CSS Reset */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* CSS Variables - Premium Color System */
:root {
    /* Brand Colors */
    --brand-primary: #6366f1;
    --brand-secondary: #8b5cf6;
    --brand-accent: #06b6d4;
    
    /* Neutral Colors */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --neutral-950: #0a0a0a;
    
    /* Semantic Colors */
    --success: #22c55e;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Typography */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    
    /* Spacing Scale */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme */
.theme-dark {
    --bg-primary: var(--neutral-950);
    --bg-secondary: var(--neutral-900);
    --bg-tertiary: var(--neutral-800);
    --bg-quaternary: var(--neutral-700);
    
    --text-primary: var(--neutral-50);
    --text-secondary: var(--neutral-300);
    --text-tertiary: var(--neutral-400);
    --text-quaternary: var(--neutral-500);
    
    --border-primary: var(--neutral-800);
    --border-secondary: var(--neutral-700);
    --border-tertiary: var(--neutral-600);
    
    --surface-primary: rgba(255, 255, 255, 0.05);
    --surface-secondary: rgba(255, 255, 255, 0.1);
    --surface-tertiary: rgba(255, 255, 255, 0.15);
}

/* Light Theme */
.theme-light {
    --bg-primary: var(--neutral-50);
    --bg-secondary: var(--neutral-100);
    --bg-tertiary: var(--neutral-200);
    --bg-quaternary: var(--neutral-300);
    
    --text-primary: var(--neutral-900);
    --text-secondary: var(--neutral-700);
    --text-tertiary: var(--neutral-600);
    --text-quaternary: var(--neutral-500);
    
    --border-primary: var(--neutral-200);
    --border-secondary: var(--neutral-300);
    --border-tertiary: var(--neutral-400);
    
    --surface-primary: rgba(255, 255, 255, 0.8);
    --surface-secondary: rgba(255, 255, 255, 0.9);
    --surface-tertiary: rgba(255, 255, 255, 1);
}

/* Base Styles */
html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow: hidden;
}

/* Premium Dashboard Layout */
.premium-dashboard {
    display: grid;
    grid-template-areas: 
        "sidebar header"
        "sidebar main";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 64px 1fr;
    height: 100vh;
    width: 100vw;
}

/* Header */
.dashboard-header {
    grid-area: header;
    background: var(--surface-primary);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-6);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    color: var(--text-tertiary);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.breadcrumb-separator {
    color: var(--text-quaternary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.theme-toggle {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--surface-tertiary);
    color: var(--text-primary);
    transform: scale(1.05);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-menu:hover {
    background: var(--surface-secondary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: white;
    box-shadow: var(--shadow-sm);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    line-height: 1.2;
}

/* Sidebar */
.dashboard-sidebar {
    grid-area: sidebar;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-logo {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    box-shadow: var(--shadow-md);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    line-height: 1.2;
}

.sidebar-nav {
    flex: 1;
    padding: var(--space-4) var(--space-3);
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--space-6);
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-quaternary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-3);
    padding: 0 var(--space-3);
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.nav-item:hover {
    background: var(--surface-secondary);
    color: var(--text-primary);
    transform: translateX(2px);
}

.nav-item.active {
    background: var(--brand-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-item.active:hover {
    background: var(--brand-primary);
    transform: translateX(0);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

/* Main Content */
.dashboard-main {
    grid-area: main;
    background: var(--bg-primary);
    overflow-y: auto;
    padding: var(--space-6);
}

.main-content {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: var(--space-8);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    line-height: 1.2;
}

.page-description {
    font-size: 1rem;
    color: var(--text-tertiary);
    line-height: 1.5;
}

/* Cards */
.card {
    background: var(--surface-primary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--border-secondary);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background: var(--surface-secondary);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-1);
}

.card-description {
    font-size: 0.875rem;
    color: var(--text-tertiary);
    line-height: 1.4;
}

.card-content {
    padding: var(--space-6);
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Responsive Grid */
@media (max-width: 1024px) {
    .grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (max-width: 768px) {
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 { 
        grid-template-columns: repeat(1, minmax(0, 1fr)); 
    }
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Profile Components */
.profile-section {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-primary);
}

.profile-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-2xl);
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    box-shadow: var(--shadow-lg);
}

.profile-details h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.profile-role {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    background: var(--brand-primary);
    color: white;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.profile-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-tertiary);
    font-weight: 500;
}

.stat-value {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 600;
}

/* Activity Components */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
    transition: all var(--transition-fast);
}

.activity-item:hover {
    background: var(--surface-tertiary);
    transform: translateX(4px);
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    background: var(--brand-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.activity-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* Quick Stats */
.quick-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.quick-stat {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
}

.quick-stat-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    background: var(--brand-accent);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.quick-stat-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.quick-stat-label {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

/* Metrics */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
}

.metric {
    text-align: center;
    padding: var(--space-4);
    background: var(--surface-secondary);
    border-radius: var(--radius-xl);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* User Stats */
.user-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.user-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
}

.user-stat-label {
    font-size: 0.875rem;
    color: var(--text-tertiary);
}

.user-stat-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Security Components */
.security-status {
    text-align: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border-radius: var(--radius-xl);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.status-safe {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.security-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.security-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.action-btn.primary {
    background: var(--brand-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.action-btn.primary:hover {
    background: var(--brand-secondary);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: var(--surface-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.action-btn.secondary:hover {
    background: var(--surface-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .premium-dashboard {
        grid-template-columns: 240px 1fr;
    }

    .dashboard-main {
        padding: var(--space-4);
    }
}

@media (max-width: 768px) {
    .premium-dashboard {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 64px 1fr;
    }

    .dashboard-sidebar {
        display: none;
    }

    .dashboard-main {
        padding: var(--space-3);
    }

    .page-title {
        font-size: 1.5rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
    }
}
