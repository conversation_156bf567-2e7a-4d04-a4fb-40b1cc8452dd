// InfernoSpaceX - Main JavaScript
// Core functionality and utilities

'use strict';

// Global app object
window.InfernoSpaceX = {
    version: '1.0.0',
    debug: false,
    
    // Configuration
    config: {
        apiBaseUrl: '/api',
        toastDuration: 5000,
        loadingDelay: 300
    },
    
    // State management
    state: {
        isLoading: false,
        user: null,
        csrfToken: null
    },
    
    // Initialize the application
    init: function() {
        this.setupEventListeners();
        this.setupSecurityFeatures();
        this.log('InfernoSpaceX initialized');
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle clicks
        document.addEventListener('click', this.handleClick.bind(this));
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', this.handleKeydown.bind(this));
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        
        // Handle beforeunload for security
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    },
    
    // Setup security features
    setupSecurityFeatures: function() {
        // Disable right-click context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
        
        // Disable F12, Ctrl+Shift+I, Ctrl+U
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                return false;
            }
        });
        
        // Disable text selection on sensitive elements
        document.addEventListener('selectstart', function(e) {
            if (e.target.classList.contains('no-select')) {
                e.preventDefault();
                return false;
            }
        });
        
        // Clear console periodically
        setInterval(function() {
            if (typeof console !== 'undefined' && console.clear) {
                console.clear();
            }
        }, 10000);
    },
    
    // Handle form submissions
    handleFormSubmit: function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            this.submitForm(form);
        }
    },
    
    // Handle clicks
    handleClick: function(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
            const action = target.dataset.action;
            this.handleAction(action, target, e);
        }
    },
    
    // Handle keyboard shortcuts
    handleKeydown: function(e) {
        // Escape key to close modals
        if (e.key === 'Escape') {
            this.closeAllModals();
        }
        
        // Enter key on buttons
        if (e.key === 'Enter' && e.target.tagName === 'BUTTON') {
            e.target.click();
        }
    },
    
    // Handle visibility changes
    handleVisibilityChange: function() {
        if (document.hidden) {
            this.log('Page hidden - pausing activities');
        } else {
            this.log('Page visible - resuming activities');
        }
    },
    
    // Handle before unload
    handleBeforeUnload: function(e) {
        // Clear sensitive data
        this.clearSensitiveData();
    },
    
    // Handle actions
    handleAction: function(action, element, event) {
        switch (action) {
            case 'toggle-password':
                this.togglePassword(element);
                break;
            case 'close-modal':
                this.closeModal(element.closest('.modal'));
                break;
            case 'show-modal':
                this.showModal(element.dataset.target);
                break;
            default:
                this.log('Unknown action:', action);
        }
    },
    
    // API request wrapper
    api: async function(endpoint, options = {}) {
        const url = this.config.apiBaseUrl + endpoint;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // Add CSRF token if available
        if (this.state.csrfToken) {
            finalOptions.headers['X-CSRF-Token'] = this.state.csrfToken;
        }
        
        try {
            this.showLoading();
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            this.log('API Error:', error);
            throw error;
        } finally {
            this.hideLoading();
        }
    },
    
    // Show loading overlay
    showLoading: function() {
        if (this.state.isLoading) return;
        
        this.state.isLoading = true;
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    },
    
    // Hide loading overlay
    hideLoading: function() {
        if (!this.state.isLoading) return;
        
        this.state.isLoading = false;
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, this.config.loadingDelay);
        }
    },
    
    // Show toast notification
    showToast: function(message, type = 'info', duration = null) {
        const container = document.getElementById('toast-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 10);
        
        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration || this.config.toastDuration);
    },
    
    // Toggle password visibility
    togglePassword: function(button) {
        const input = button.parentNode.querySelector('input[type="password"], input[type="text"]');
        const icon = button.querySelector('.toggle-icon');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.textContent = '🙈';
        } else {
            input.type = 'password';
            icon.textContent = '👁️';
        }
    },
    
    // Show modal
    showModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            
            // Focus first input
            const firstInput = modal.querySelector('input, button');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    },
    
    // Close modal
    closeModal: function(modal) {
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    },
    
    // Close all modals
    closeAllModals: function() {
        const modals = document.querySelectorAll('.modal:not(.hidden)');
        modals.forEach(modal => this.closeModal(modal));
    },
    
    // Form validation
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'Это поле обязательно');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
            
            // Email validation
            if (input.type === 'email' && input.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(input.value)) {
                    this.showFieldError(input, 'Введите корректный email');
                    isValid = false;
                }
            }
            
            // Password confirmation
            if (input.name === 'confirm-password') {
                const passwordInput = form.querySelector('input[name="password"]');
                if (passwordInput && input.value !== passwordInput.value) {
                    this.showFieldError(input, 'Пароли не совпадают');
                    isValid = false;
                }
            }
        });
        
        return isValid;
    },
    
    // Show field error
    showFieldError: function(input, message) {
        input.classList.add('error');
        
        let errorElement = input.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            input.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    },
    
    // Clear field error
    clearFieldError: function(input) {
        input.classList.remove('error');
        const errorElement = input.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    },
    
    // Clear sensitive data
    clearSensitiveData: function() {
        // Clear form inputs
        const sensitiveInputs = document.querySelectorAll('input[type="password"]');
        sensitiveInputs.forEach(input => {
            input.value = '';
        });
        
        // Clear local storage
        try {
            localStorage.clear();
            sessionStorage.clear();
        } catch (e) {
            this.log('Could not clear storage:', e);
        }
    },
    
    // Logging utility
    log: function(...args) {
        if (this.debug && console && console.log) {
            console.log('[InfernoSpaceX]', ...args);
        }
    },
    
    // Error handling
    handleError: function(error, context = '') {
        this.log('Error in', context, ':', error);
        
        let message = 'Произошла ошибка';
        if (error.message) {
            message = error.message;
        }
        
        this.showToast(message, 'error');
    },
    
    // Utility functions
    utils: {
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Throttle function
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        // Generate random string
        randomString: function(length = 16) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        },
        
        // Format date
        formatDate: function(date) {
            return new Intl.DateTimeFormat('ru-RU', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(new Date(date));
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.InfernoSpaceX.init();
});

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.InfernoSpaceX;
}
