// Midnight Dashboard - Enhanced JavaScript
class MidnightDashboard {
    constructor() {
        this.particles = [];
        this.particleCount = 50;
        this.animationId = null;
        this.init();
    }

    init() {
        this.createParticleSystem();
        this.initEventListeners();
        this.startAnimations();
        this.loadUserData();
        this.initRealTimeUpdates();
    }

    // Particle System
    createParticleSystem() {
        const container = document.getElementById('particles-container');
        if (!container) return;

        // Clear existing particles
        container.innerHTML = '';
        this.particles = [];

        for (let i = 0; i < this.particleCount; i++) {
            this.createParticle(container);
        }
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random position and properties
        const x = Math.random() * window.innerWidth;
        const y = window.innerHeight + Math.random() * 100;
        const size = Math.random() * 3 + 1;
        const speed = Math.random() * 2 + 1;
        const opacity = Math.random() * 0.6 + 0.2;
        
        particle.style.left = x + 'px';
        particle.style.top = y + 'px';
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.opacity = opacity;
        particle.style.animationDuration = (8 / speed) + 's';
        particle.style.animationDelay = Math.random() * 8 + 's';
        
        // Random color
        const colors = ['#8b5cf6', '#3b82f6', '#06b6d4', '#10b981', '#f59e0b'];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        
        container.appendChild(particle);
        this.particles.push(particle);
        
        // Remove and recreate particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.remove();
                this.createParticle(container);
            }
        }, 8000 + Math.random() * 2000);
    }

    // Event Listeners
    initEventListeners() {
        // Logout functionality
        const logoutBtn = document.getElementById('logout-btn');
        const logoutModal = document.getElementById('logout-modal');
        const cancelLogout = document.getElementById('cancel-logout');
        const confirmLogout = document.getElementById('confirm-logout');

        if (logoutBtn && logoutModal) {
            logoutBtn.addEventListener('click', () => {
                logoutModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });
        }

        if (cancelLogout && logoutModal) {
            cancelLogout.addEventListener('click', () => {
                logoutModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            });
        }

        if (confirmLogout) {
            confirmLogout.addEventListener('click', () => {
                this.performLogout();
            });
        }

        // Action cards
        this.initActionCards();

        // Stat cards hover effects
        this.initStatCards();

        // Window resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Close modal on outside click
        if (logoutModal) {
            logoutModal.addEventListener('click', (e) => {
                if (e.target === logoutModal) {
                    logoutModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            });
        }
    }

    initActionCards() {
        const newMissionBtn = document.getElementById('new-mission-btn');
        const analyticsBtn = document.getElementById('analytics-btn');
        const settingsBtn = document.getElementById('settings-btn');

        if (newMissionBtn) {
            newMissionBtn.addEventListener('click', () => {
                this.showToast('Запуск новой миссии...', 'info');
                this.simulateAction('mission');
            });
        }

        if (analyticsBtn) {
            analyticsBtn.addEventListener('click', () => {
                this.showToast('Загрузка аналитики...', 'info');
                this.simulateAction('analytics');
            });
        }

        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showToast('Открытие настроек...', 'info');
                this.simulateAction('settings');
            });
        }
    }

    initStatCards() {
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.addEventListener('click', () => {
                this.animateStatCard(card, index);
            });
        });
    }

    // Animations
    startAnimations() {
        this.animateNumbers();
        this.pulseStatusIndicator();
    }

    animateNumbers() {
        const numbers = document.querySelectorAll('.stat-number');
        numbers.forEach(number => {
            const text = number.textContent;
            if (!isNaN(text) && text !== 'Онлайн') {
                this.countUp(number, 0, parseInt(text), 2000);
            }
        });
    }

    countUp(element, start, end, duration) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    animateStatCard(card, index) {
        card.style.transform = 'scale(0.95)';
        setTimeout(() => {
            card.style.transform = '';
        }, 150);

        const messages = [
            'Активные миссии обновлены',
            'Статистика запусков загружена',
            'Данные точности обновлены',
            'Статус системы проверен'
        ];

        this.showToast(messages[index], 'success');
    }

    pulseStatusIndicator() {
        const statusDot = document.querySelector('.status-dot');
        if (statusDot) {
            setInterval(() => {
                statusDot.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    statusDot.style.transform = 'scale(1)';
                }, 200);
            }, 3000);
        }
    }

    // Data Management
    loadUserData() {
        // Simulate loading user data
        setTimeout(() => {
            const userName = document.getElementById('user-name');
            if (userName) {
                userName.textContent = 'Командир';
                userName.style.opacity = '1';
            }
        }, 500);
    }

    initRealTimeUpdates() {
        // Simulate real-time updates
        setInterval(() => {
            this.updateStats();
        }, 30000); // Update every 30 seconds
    }

    updateStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach((stat, index) => {
            if (index === 0) { // Active missions
                const current = parseInt(stat.textContent);
                const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
                const newValue = Math.max(0, current + change);
                this.countUp(stat, current, newValue, 1000);
            }
        });
    }

    // Utility Functions
    simulateAction(action) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
            
            setTimeout(() => {
                loadingOverlay.classList.add('hidden');
                this.showToast(`${action} выполнено успешно!`, 'success');
            }, 2000);
        }
    }

    performLogout() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('hidden');
        }
        
        setTimeout(() => {
            window.location.href = '/logout';
        }, 1000);
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }

    handleResize() {
        // Recreate particles on resize
        if (window.innerWidth > 768) {
            this.createParticleSystem();
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.midnightDashboard = new MidnightDashboard();
});

// Export for global access
window.MidnightDashboard = MidnightDashboard;
