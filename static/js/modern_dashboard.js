// Modern Dashboard JavaScript
class ModernDashboard {
    constructor() {
        this.currentTab = 'profile';
        this.init();
    }

    init() {
        this.setupTabNavigation();
        this.setupLogout();
        this.setupProbivTools();
        this.setupChat();
        this.setupSecurity();
        this.setupThemeSwitch();
        this.loadUserInfo();
    }

    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');
                
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
                
                this.currentTab = tabId;
                
                // Load tab-specific data
                this.loadTabData(tabId);
            });
        });
    }

    setupLogout() {
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch('/api/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        window.location.href = '/login';
                    }
                } catch (error) {
                    console.error('Logout error:', error);
                }
            });
        }
    }

    setupProbivTools() {
        // IP Analysis
        const analyzeIpBtn = document.getElementById('analyze-ip');
        const ipInput = document.getElementById('ip-input');
        const ipResults = document.getElementById('ip-results');

        if (analyzeIpBtn) {
            analyzeIpBtn.addEventListener('click', async () => {
                const ip = ipInput.value.trim();
                if (!ip) return;

                ipResults.innerHTML = '<div class="loading">🔍 Анализируем IP адрес...</div>';
                
                try {
                    const response = await fetch(`/api/security/ip-analysis/${ip}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        this.displayIPAnalysis(data, ipResults);
                    } else {
                        ipResults.innerHTML = `<div class="error">❌ Ошибка: ${data.error}</div>`;
                    }
                } catch (error) {
                    ipResults.innerHTML = `<div class="error">❌ Ошибка соединения: ${error.message}</div>`;
                }
            });
        }

        // User Search
        const searchUserBtn = document.getElementById('search-user');
        const userInput = document.getElementById('user-input');
        const userResults = document.getElementById('user-results');

        if (searchUserBtn) {
            searchUserBtn.addEventListener('click', () => {
                const username = userInput.value.trim();
                if (!username) return;

                userResults.innerHTML = `
                    <div class="user-info">
                        <div class="info-line">👤 Пользователь: ${username}</div>
                        <div class="info-line">📅 Дата регистрации: Неизвестно</div>
                        <div class="info-line">🌍 Последний IP: Скрыт</div>
                        <div class="info-line">⚡ Статус: Поиск в базе данных...</div>
                    </div>
                `;
            });
        }
    }

    setupChat() {
        const sendBtn = document.getElementById('send-message');
        const chatInput = document.getElementById('chat-input');
        const chatMessages = document.getElementById('chat-messages');

        // Load existing messages
        this.loadChatMessages();

        if (sendBtn && chatInput) {
            const sendMessage = async () => {
                const message = chatInput.value.trim();
                if (!message) return;

                chatInput.value = '';
                sendBtn.disabled = true;

                try {
                    const response = await fetch('/api/chat/send', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ message })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.addChatMessage(data.message);
                    } else {
                        this.showChatError(data.error);
                    }
                } catch (error) {
                    this.showChatError('Ошибка отправки сообщения');
                } finally {
                    sendBtn.disabled = false;
                }
            };

            sendBtn.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // Auto-refresh messages every 5 seconds
        setInterval(() => {
            this.loadChatMessages();
        }, 5000);
    }

    async loadChatMessages() {
        try {
            const response = await fetch('/api/chat/messages');
            const data = await response.json();

            if (data.success) {
                const chatMessages = document.getElementById('chat-messages');
                chatMessages.innerHTML = '';

                data.messages.forEach(message => {
                    this.addChatMessage(message);
                });
            }
        } catch (error) {
            console.error('Error loading chat messages:', error);
        }
    }

    showChatError(message) {
        const chatMessages = document.getElementById('chat-messages');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message error';
        errorDiv.innerHTML = `
            <div class="message-content error-message">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
            </div>
        `;
        chatMessages.appendChild(errorDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    setupSecurity() {
        const refreshBtn = document.getElementById('refresh-stats');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadSecurityStats();
            });
        }
    }

    setupThemeSwitch() {
        const themeSelector = document.getElementById('theme-selector');
        if (themeSelector) {
            // Load saved theme
            const savedTheme = localStorage.getItem('inferno-theme') || 'dark';
            themeSelector.value = savedTheme;
            this.applyTheme(savedTheme);

            // Handle theme change
            themeSelector.addEventListener('change', (e) => {
                const newTheme = e.target.value;
                this.applyTheme(newTheme);
                localStorage.setItem('inferno-theme', newTheme);
            });
        }
    }

    applyTheme(theme) {
        const body = document.body;

        // Remove existing theme classes
        body.classList.remove('theme-dark', 'theme-light', 'theme-auto');

        if (theme === 'light') {
            body.classList.add('theme-light');
        } else if (theme === 'auto') {
            body.classList.add('theme-auto');
            // Check system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches) {
                body.classList.add('theme-light');
            }
        } else {
            body.classList.add('theme-dark');
        }
    }

    loadUserInfo() {
        // Load user information
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                if (data.authenticated) {
                    document.getElementById('username').textContent = data.username;
                    document.getElementById('profile-username').textContent = data.username;

                    // Update nav role
                    const navRole = document.getElementById('nav-user-role');
                    if (navRole) {
                        navRole.textContent = data.role || 'Пользователь';
                    }

                    // Update avatars with first letter
                    const firstLetter = data.username.charAt(0).toUpperCase();

                    const navAvatar = document.getElementById('nav-user-avatar');
                    if (navAvatar) {
                        navAvatar.textContent = firstLetter;
                    }

                    const profileAvatar = document.getElementById('profile-avatar');
                    if (profileAvatar) {
                        profileAvatar.textContent = firstLetter;
                    }
                }
            })
            .catch(error => console.error('Error loading user info:', error));
    }

    loadTabData(tabId) {
        switch (tabId) {
            case 'security':
                this.loadSecurityStats();
                break;
            case 'profile':
                this.loadProfileData();
                break;
        }
    }

    async loadSecurityStats() {
        try {
            const response = await fetch('/api/security/stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('blocked-ips').textContent = data.statistics.total_blocked_ips || 0;
                document.getElementById('recent-threats').textContent = data.statistics.recent_threat_events || 0;
            }
        } catch (error) {
            console.error('Error loading security stats:', error);
        }
    }

    async loadProfileData() {
        try {
            const response = await fetch('/api/profile');
            const data = await response.json();

            if (data.success) {
                const profile = data.profile;

                // Update profile information
                document.getElementById('profile-username').textContent = profile.username;
                document.getElementById('hardware-id').textContent = profile.partial_hwid;

                // Update role with proper styling
                const roleElement = document.getElementById('profile-role');
                roleElement.textContent = profile.role;
                roleElement.className = `role-badge-large ${profile.role_class}`;

                // Update dates
                if (profile.created_at) {
                    const createdDate = new Date(profile.created_at).toLocaleDateString('ru-RU');
                    document.getElementById('registration-date').textContent = createdDate;
                }

                if (profile.last_login) {
                    const lastLogin = new Date(profile.last_login).toLocaleString('ru-RU');
                    document.getElementById('last-login').textContent = lastLogin;
                } else {
                    document.getElementById('last-login').textContent = 'Первый вход';
                }

                // Update avatar with first letter
                const avatarElement = document.getElementById('profile-avatar');
                if (avatarElement) {
                    avatarElement.textContent = profile.username.charAt(0).toUpperCase();
                }
            }
        } catch (error) {
            console.error('Error loading profile data:', error);
        }
    }

    displayIPAnalysis(data, container) {
        const risk = data.risk_assessment;
        const threat = data.threat_profile;
        
        let riskColor = '#10b981'; // green
        if (risk.risk_level === 'CRITICAL') riskColor = '#ef4444';
        else if (risk.risk_level === 'HIGH') riskColor = '#f97316';
        else if (risk.risk_level === 'MEDIUM') riskColor = '#eab308';

        container.innerHTML = `
            <div class="ip-analysis-result">
                <div class="analysis-header">
                    <h4>🌍 Анализ IP: ${data.ip}</h4>
                </div>
                <div class="analysis-content">
                    <div class="info-line">
                        <span>🚨 Уровень риска:</span>
                        <span style="color: ${riskColor}; font-weight: bold;">${risk.risk_level} (${risk.risk_score})</span>
                    </div>
                    <div class="info-line">
                        <span>🌍 Страна:</span>
                        <span>${risk.intelligence.country || 'Неизвестно'}</span>
                    </div>
                    <div class="info-line">
                        <span>🏢 ISP:</span>
                        <span>${risk.intelligence.isp || 'Неизвестно'}</span>
                    </div>
                    <div class="info-line">
                        <span>⚠️ Факторы риска:</span>
                        <span>${risk.risk_factors.join(', ') || 'Нет'}</span>
                    </div>
                    <div class="info-line">
                        <span>💡 Рекомендация:</span>
                        <span>${risk.recommendation}</span>
                    </div>
                    <div class="info-line">
                        <span>📊 Запросов:</span>
                        <span>${threat.profile?.request_count || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }

    addChatMessage(messageData) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');

        const timestamp = messageData.timestamp ? new Date(messageData.timestamp * 1000) : new Date();
        const timeString = timestamp.toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Determine message type and styling
        let messageClass = 'message';
        let roleColor = '#667eea';
        let avatarLetter = messageData.username ? messageData.username.charAt(0).toUpperCase() : 'U';

        if (messageData.type === 'system') {
            messageClass += ' system';
            roleColor = '#10b981';
        } else if (messageData.role === 'Admin') {
            roleColor = '#ef4444';
        } else if (messageData.role === 'Premium') {
            roleColor = '#f59e0b';
        } else if (messageData.role === 'Subscription') {
            roleColor = '#8b5cf6';
        }

        // Check if this is current user's message
        const currentUsername = document.getElementById('username').textContent;
        if (messageData.username === currentUsername && messageData.type !== 'system') {
            messageClass += ' user';
        }

        messageDiv.className = messageClass;

        if (messageData.type === 'system') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="user-info">
                        <span class="username">Система</span>
                    </div>
                    <div class="message-time">${timeString}</div>
                </div>
                <div class="message-content">
                    ${messageData.message}
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="user-info">
                        <div class="message-avatar" style="background: linear-gradient(135deg, ${roleColor}, ${roleColor}dd);">
                            ${avatarLetter}
                        </div>
                        <span class="username">${messageData.username}</span>
                        <span class="role-badge" style="background: linear-gradient(135deg, ${roleColor}, ${roleColor}aa);">${messageData.role}</span>
                    </div>
                    <div class="message-time">${timeString}</div>
                </div>
                <div class="message-content">
                    ${messageData.message}
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernDashboard();
});

// Add modern chat message styles
const style = document.createElement('style');
style.textContent = `
    .message {
        margin-bottom: 1.5rem;
        animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 85%;
    }

    @keyframes messageSlideIn {
        from { opacity: 0; transform: translateY(15px) scale(0.95); }
        to { opacity: 1; transform: translateY(0) scale(1); }
    }

    .message-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
        font-size: 0.85rem;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .username {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
    }

    .role-badge {
        padding: 0.2rem 0.6rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .message-time {
        color: rgba(255, 255, 255, 0.5);
        font-size: 0.75rem;
        margin-left: auto;
    }

    .message-content {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem 1.25rem;
        border-radius: 18px;
        word-wrap: break-word;
        line-height: 1.5;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        margin-left: 42px;
    }

    .message-content::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 12px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid rgba(255, 255, 255, 0.1);
    }

    .message.system {
        max-width: 100%;
        text-align: center;
    }

    .message.system .message-content {
        background: rgba(16, 185, 129, 0.15);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: #10b981;
        margin-left: 0;
        border-radius: 12px;
        font-style: italic;
    }

    .message.system .message-content::before {
        display: none;
    }

    .message.system .message-header {
        justify-content: center;
    }

    .error-message {
        background: rgba(239, 68, 68, 0.15);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #ef4444;
    }

    /* User message styling */
    .message.user {
        margin-left: auto;
        text-align: right;
    }

    .message.user .message-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        margin-left: 0;
        margin-right: 42px;
    }

    .message.user .message-content::before {
        left: auto;
        right: -8px;
        border-left: 8px solid #667eea;
        border-right: none;
    }

    .message.user .message-header {
        flex-direction: row-reverse;
    }

    /* Light theme adjustments */
    .theme-light .message-content {
        background: rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.1);
        color: #1e293b;
    }

    .theme-light .username {
        color: #1e293b;
    }

    .theme-light .message-time {
        color: rgba(0, 0, 0, 0.5);
    }

    .theme-light .message-content::before {
        border-right-color: rgba(0, 0, 0, 0.1);
    }

    .theme-light .message.user .message-content::before {
        border-left-color: #667eea;
    }
    
    .loading, .error {
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }
    
    .loading {
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .error {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
    }
    
    .ip-analysis-result {
        background: rgba(16, 185, 129, 0.1);
        border-radius: 8px;
        padding: 1rem;
    }
    
    .analysis-header h4 {
        color: #10b981;
        margin-bottom: 1rem;
    }
    
    .info-line {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .info-line:last-child {
        border-bottom: none;
    }
    
    .user-info {
        background: rgba(102, 126, 234, 0.1);
        border-radius: 8px;
        padding: 1rem;
    }
`;
document.head.appendChild(style);
