// Main Panel JavaScript
class MainPanel {
    constructor() {
        this.currentTab = 'profile';
        this.chatMessages = [];
        this.sessionStartTime = Date.now();
        this.stats = {
            searches: 0,
            messages: 0,
            sessionTime: 0
        };
        
        this.init();
    }

    init() {
        this.initTabs();
        this.initProfile();
        this.initOSINT();
        this.initChat();
        this.initModals();
        this.startSessionTimer();
        this.loadInitialData();
    }

    // Tab Management
    initTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                this.switchTab(tabId);
            });
        });
    }

    switchTab(tabId) {
        // Update buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tabId}-panel`).classList.add('active');

        this.currentTab = tabId;

        // Load tab-specific data
        if (tabId === 'chat') {
            this.loadChatMessages();
        }
    }

    // Profile Management
    initProfile() {
        this.updateStats();
    }

    updateStats() {
        const sessionMinutes = Math.floor((Date.now() - this.sessionStartTime) / 60000);
        
        document.getElementById('total-searches').textContent = this.stats.searches;
        document.getElementById('total-messages').textContent = this.stats.messages;
        document.getElementById('session-time').textContent = sessionMinutes;
    }

    startSessionTimer() {
        setInterval(() => {
            this.updateStats();
        }, 60000); // Update every minute
    }

    // OSINT Tools
    initOSINT() {
        const analyzeIpBtn = document.getElementById('analyze-ip-btn');
        const analyzeDomainBtn = document.getElementById('analyze-domain-btn');

        analyzeIpBtn.addEventListener('click', () => {
            const ip = document.getElementById('ip-input').value.trim();
            if (ip) {
                this.analyzeIP(ip);
            } else {
                this.showToast('Введите IP адрес', 'warning');
            }
        });

        analyzeDomainBtn.addEventListener('click', () => {
            const domain = document.getElementById('domain-input').value.trim();
            if (domain) {
                this.analyzeDomain(domain);
            } else {
                this.showToast('Введите домен', 'warning');
            }
        });

        // Enter key support
        document.getElementById('ip-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') analyzeIpBtn.click();
        });

        document.getElementById('domain-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') analyzeDomainBtn.click();
        });
    }

    async analyzeIP(ip) {
        const resultsArea = document.getElementById('ip-results');
        const btn = document.getElementById('analyze-ip-btn');
        
        btn.disabled = true;
        btn.textContent = 'Анализируем...';
        resultsArea.textContent = 'Выполняется анализ IP адреса...';

        try {
            const response = await fetch(`/api/security/ip-analysis/${ip}`);
            const data = await response.json();

            if (data.success) {
                const result = this.formatIPAnalysis(data);
                resultsArea.textContent = result;
                this.stats.searches++;
                this.showToast('Анализ IP завершен', 'success');
            } else {
                resultsArea.textContent = `Ошибка: ${data.error}`;
                this.showToast('Ошибка анализа IP', 'error');
            }
        } catch (error) {
            resultsArea.textContent = `Ошибка сети: ${error.message}`;
            this.showToast('Ошибка сети', 'error');
        }

        btn.disabled = false;
        btn.textContent = 'Анализировать';
        this.updateStats();
    }

    formatIPAnalysis(data) {
        const { risk_assessment, threat_profile } = data;
        
        let result = `=== АНАЛИЗ IP: ${data.ip} ===\n\n`;
        
        if (risk_assessment) {
            result += `🔍 БАЗОВАЯ ИНФОРМАЦИЯ:\n`;
            result += `• Страна: ${risk_assessment.country || 'Неизвестно'}\n`;
            result += `• Город: ${risk_assessment.city || 'Неизвестно'}\n`;
            result += `• ISP: ${risk_assessment.isp || 'Неизвестно'}\n`;
            result += `• Тип: ${risk_assessment.connection_type || 'Неизвестно'}\n\n`;
        }

        if (threat_profile) {
            result += `⚠️ ОЦЕНКА УГРОЗ:\n`;
            result += `• Уровень риска: ${threat_profile.risk_level || 'Неизвестно'}\n`;
            result += `• Оценка: ${threat_profile.risk_score || 0}/100\n`;
            result += `• Статус: ${threat_profile.is_malicious ? 'Подозрительный' : 'Чистый'}\n\n`;
        }

        result += `📊 ДОПОЛНИТЕЛЬНО:\n`;
        result += `• Время анализа: ${new Date().toLocaleString()}\n`;
        result += `• Источник данных: InfernoSpaceX Intelligence\n`;

        return result;
    }

    async analyzeDomain(domain) {
        const resultsArea = document.getElementById('domain-results');
        const btn = document.getElementById('analyze-domain-btn');

        btn.disabled = true;
        btn.textContent = 'Анализируем...';
        resultsArea.textContent = 'Выполняется анализ домена...';

        try {
            const response = await fetch(`/api/osint/domain/${domain}`);
            const data = await response.json();

            if (data.success) {
                const result = this.formatDomainAnalysis(data.analysis);
                resultsArea.textContent = result;
                this.stats.searches++;
                this.showToast('Анализ домена завершен', 'success');
            } else {
                resultsArea.textContent = `Ошибка: ${data.error}`;
                this.showToast('Ошибка анализа домена', 'error');
            }
        } catch (error) {
            resultsArea.textContent = `Ошибка сети: ${error.message}`;
            this.showToast('Ошибка сети', 'error');
        }

        btn.disabled = false;
        btn.textContent = 'Анализировать';
        this.updateStats();
    }

    formatDomainAnalysis(analysis) {
        const { domain, dns_info, ssl_info, http_info, security_info } = analysis;

        let result = `=== АНАЛИЗ ДОМЕНА: ${domain} ===\n\n`;

        // DNS Information
        result += `🌐 DNS ИНФОРМАЦИЯ:\n`;
        if (dns_info.resolved) {
            result += `• IP адрес: ${dns_info.ip}\n`;
            result += `• DNS статус: Разрешен\n`;
        } else {
            result += `• DNS статус: Ошибка разрешения\n`;
            result += `• Ошибка: ${dns_info.error || 'Неизвестно'}\n`;
        }
        result += `\n`;

        // SSL Information
        result += `🔒 SSL СЕРТИФИКАТ:\n`;
        if (ssl_info.valid) {
            result += `• Статус: Действительный\n`;
            result += `• Издатель: ${ssl_info.issuer}\n`;
            result += `• Истекает: ${ssl_info.expires}\n`;
        } else {
            result += `• Статус: Недействительный или отсутствует\n`;
            result += `• Ошибка: ${ssl_info.error || 'SSL недоступен'}\n`;
        }
        result += `\n`;

        // HTTP Information
        result += `🌍 HTTP ДОСТУПНОСТЬ:\n`;
        if (http_info.accessible) {
            result += `• Статус: Доступен\n`;
            result += `• HTTP код: ${http_info.status_code}\n`;
            result += `• Редиректы: ${http_info.redirects ? 'Есть' : 'Нет'}\n`;
        } else {
            result += `• Статус: Недоступен\n`;
            result += `• Ошибка: ${http_info.error || 'HTTP недоступен'}\n`;
        }
        result += `\n`;

        // Security Assessment
        result += `⚠️ ОЦЕНКА БЕЗОПАСНОСТИ:\n`;
        result += `• Уровень риска: ${security_info.risk_level}\n`;
        result += `• Репутация: ${security_info.reputation}\n`;
        result += `• В черном списке: ${security_info.blacklisted ? 'Да' : 'Нет'}\n\n`;

        result += `📊 ДОПОЛНИТЕЛЬНО:\n`;
        result += `• Время анализа: ${new Date().toLocaleString()}\n`;
        result += `• Источник данных: InfernoSpaceX Intelligence\n`;

        return result;
    }

    // Chat Management
    initChat() {
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        const charCount = document.getElementById('char-count');

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        chatInput.addEventListener('input', () => {
            const length = chatInput.value.length;
            charCount.textContent = length;
            
            if (length > 450) {
                charCount.style.color = 'var(--error)';
            } else if (length > 400) {
                charCount.style.color = 'var(--warning)';
            } else {
                charCount.style.color = 'var(--text-muted)';
            }
        });
    }

    async loadChatMessages() {
        try {
            const response = await fetch('/api/chat/messages');
            const data = await response.json();

            if (data.success) {
                this.chatMessages = data.messages;
                this.renderChatMessages();
            }
        } catch (error) {
            console.error('Error loading chat messages:', error);
        }
    }

    renderChatMessages() {
        const container = document.getElementById('chat-messages');
        container.innerHTML = '';

        this.chatMessages.forEach(message => {
            const messageEl = this.createMessageElement(message);
            container.appendChild(messageEl);
        });

        container.scrollTop = container.scrollHeight;
    }

    createMessageElement(message) {
        const div = document.createElement('div');
        div.className = `chat-message ${message.type === 'system' ? 'system' : ''}`;

        if (message.type === 'system') {
            div.innerHTML = `<div class="message-text">${message.message}</div>`;
        } else {
            div.innerHTML = `
                <div class="message-avatar">${message.username[0].toUpperCase()}</div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-username">${message.username}</span>
                        <span class="message-role">${message.role}</span>
                        <span class="message-time">${this.formatTime(message.timestamp)}</span>
                    </div>
                    <div class="message-text">${message.message}</div>
                </div>
            `;
        }

        return div;
    }

    async sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message) return;

        const sendBtn = document.getElementById('send-btn');
        sendBtn.disabled = true;

        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const data = await response.json();

            if (data.success) {
                this.chatMessages.push(data.message);
                this.renderChatMessages();
                this.stats.messages++;
                this.updateStats();
                input.value = '';
                document.getElementById('char-count').textContent = '0';
                
                // Show chat indicator
                const indicator = document.getElementById('chat-indicator');
                indicator.classList.add('active');
                setTimeout(() => indicator.classList.remove('active'), 3000);
            } else {
                this.showToast('Ошибка отправки сообщения', 'error');
            }
        } catch (error) {
            this.showToast('Ошибка сети', 'error');
        }

        sendBtn.disabled = false;
    }

    formatTime(timestamp) {
        return new Date(timestamp * 1000).toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Modal Management
    initModals() {
        const logoutBtn = document.getElementById('logout-btn');
        const logoutModal = document.getElementById('logout-modal');
        const cancelLogout = document.getElementById('cancel-logout');
        const confirmLogout = document.getElementById('confirm-logout');

        logoutBtn.addEventListener('click', () => {
            logoutModal.classList.add('active');
        });

        cancelLogout.addEventListener('click', () => {
            logoutModal.classList.remove('active');
        });

        confirmLogout.addEventListener('click', () => {
            this.logout();
        });

        // Close modal on outside click
        logoutModal.addEventListener('click', (e) => {
            if (e.target === logoutModal) {
                logoutModal.classList.remove('active');
            }
        });
    }

    async logout() {
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/logout', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                window.location.href = '/login';
            }
        } catch (error) {
            this.showToast('Ошибка выхода', 'error');
        }
        
        this.showLoading(false);
    }

    // Utility Methods
    async loadInitialData() {
        // Load profile data
        try {
            const response = await fetch('/api/profile');
            const data = await response.json();
            
            if (data.success) {
                // Update profile information if needed
            }
        } catch (error) {
            console.error('Error loading profile:', error);
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('active');
        } else {
            overlay.classList.remove('active');
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 4000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mainPanel = new MainPanel();
});
