// InfernoSpaceX Premium Dashboard
class PremiumDashboard {
    constructor() {
        this.currentTab = 'profile';
        this.theme = localStorage.getItem('inferno-theme') || 'dark';
        this.sessionStartTime = Date.now();
        this.init();
    }

    init() {
        this.setupTheme();
        this.setupNavigation();
        this.setupUserMenu();
        this.loadUserData();
        this.startSessionTimer();
        this.setupLogout();
    }

    // Theme System
    setupTheme() {
        const container = document.getElementById('dashboard-container');
        const themeToggle = document.getElementById('theme-toggle');
        
        this.applyTheme(this.theme);
        
        themeToggle.addEventListener('click', () => {
            this.theme = this.theme === 'dark' ? 'light' : 'dark';
            this.applyTheme(this.theme);
            localStorage.setItem('inferno-theme', this.theme);
        });
    }

    applyTheme(theme) {
        const container = document.getElementById('dashboard-container');
        const themeIcon = document.querySelector('#theme-toggle i');
        
        container.classList.remove('theme-dark', 'theme-light');
        container.classList.add(`theme-${theme}`);
        
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }

    // Navigation System
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');
        const currentPageElement = document.getElementById('current-page');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const tabId = item.getAttribute('data-tab');
                
                // Update active states
                navItems.forEach(nav => nav.classList.remove('active'));
                tabContents.forEach(tab => tab.classList.remove('active'));
                
                item.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
                
                // Update breadcrumb
                const tabName = item.querySelector('span').textContent;
                currentPageElement.textContent = tabName;
                
                this.currentTab = tabId;
                this.onTabChange(tabId);
            });
        });
    }

    onTabChange(tabId) {
        switch (tabId) {
            case 'profile':
                this.loadProfileData();
                break;
            case 'security':
                this.loadSecurityData();
                break;
            case 'analytics':
                this.loadAnalyticsData();
                break;
        }
    }

    // User Menu
    setupUserMenu() {
        const userMenu = document.getElementById('user-menu');
        
        userMenu.addEventListener('click', () => {
            // Future: Show dropdown menu
            console.log('User menu clicked');
        });
    }

    // Data Loading
    async loadUserData() {
        try {
            const response = await fetch('/api/profile');
            const data = await response.json();
            
            if (data.success) {
                this.updateUserInterface(data.profile);
            } else {
                this.showNotification('Failed to load user data', 'error');
            }
        } catch (error) {
            console.error('Error loading user data:', error);
            this.showNotification('Connection error', 'error');
        }
    }

    updateUserInterface(profile) {
        const username = profile.username || 'User';
        const role = profile.role || 'User';
        const firstLetter = username.charAt(0).toUpperCase();
        
        // Update all username elements
        document.getElementById('header-username').textContent = username;
        document.getElementById('profile-name').textContent = username;
        document.getElementById('header-role').textContent = role;
        
        // Update avatars
        document.getElementById('header-avatar').textContent = firstLetter;
        document.getElementById('profile-avatar').textContent = firstLetter;
        
        // Update role badge
        const roleBadge = document.getElementById('profile-role-badge');
        roleBadge.textContent = role;
        roleBadge.className = `profile-role role-${role.toLowerCase()}`;
        
        // Update profile stats
        if (profile.created_at) {
            const createdDate = new Date(profile.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('member-since').textContent = createdDate;
        }
        
        if (profile.last_login) {
            const lastLogin = new Date(profile.last_login).toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('last-login').textContent = lastLogin;
        }
        
        if (profile.partial_hwid) {
            document.getElementById('hardware-id').textContent = profile.partial_hwid;
        }
    }

    loadProfileData() {
        // Profile-specific data loading
        this.getUserLocation();
    }

    async getUserLocation() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            
            if (data.city && data.country_name) {
                const location = `${data.city}, ${data.country_name}`;
                document.getElementById('user-location').textContent = location;
            }
        } catch (error) {
            console.error('Error getting location:', error);
        }
    }

    async loadSecurityData() {
        try {
            const response = await fetch('/api/security/stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('blocked-ips').textContent = data.statistics.total_blocked_ips || 0;
                document.getElementById('threats-detected').textContent = data.statistics.recent_threat_events || 0;
            }
        } catch (error) {
            console.error('Error loading security data:', error);
        }
    }

    loadAnalyticsData() {
        // Analytics-specific data loading
        console.log('Loading analytics data...');
    }

    // Session Timer
    startSessionTimer() {
        const updateTimer = () => {
            const elapsed = Date.now() - this.sessionStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const hours = Math.floor(minutes / 60);
            
            let timeString;
            if (hours > 0) {
                timeString = `${hours}h ${minutes % 60}m`;
            } else {
                timeString = `${minutes}m`;
            }
            
            const sessionTimeElement = document.getElementById('session-time');
            if (sessionTimeElement) {
                sessionTimeElement.textContent = timeString;
            }
        };
        
        updateTimer();
        setInterval(updateTimer, 60000); // Update every minute
    }

    // Logout
    setupLogout() {
        const logoutBtn = document.getElementById('logout-btn');
        
        logoutBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    this.showNotification('Logged out successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1000);
                } else {
                    this.showNotification('Logout failed', 'error');
                }
            } catch (error) {
                console.error('Logout error:', error);
                this.showNotification('Connection error', 'error');
            }
        });
    }

    // Notification System
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(n => n.remove());
        
        // Create notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <div class="notification-content">
                <i class="${icon}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => notification.classList.add('show'), 10);
        
        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // Utility Methods
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    formatTime(dateString) {
        return new Date(dateString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    new PremiumDashboard();
});

// Add notification styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        backdrop-filter: blur(20px);
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 500;
        font-size: 14px;
    }
    
    .notification-success {
        background: rgba(34, 197, 94, 0.9);
        color: white;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }
    
    .notification-error {
        background: rgba(239, 68, 68, 0.9);
        color: white;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .notification-warning {
        background: rgba(245, 158, 11, 0.9);
        color: white;
        border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .notification-info {
        background: rgba(59, 130, 246, 0.9);
        color: white;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }
`;
document.head.appendChild(notificationStyles);
