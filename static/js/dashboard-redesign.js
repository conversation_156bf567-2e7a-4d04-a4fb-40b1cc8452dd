// InfernoSpaceX Dashboard - Professional Implementation
class ProfessionalDashboard {
    constructor() {
        this.currentTab = 'profile';
        this.sessionStartTime = Date.now();
        this.init();
    }

    init() {
        this.setupTabNavigation();
        this.setupThemeSystem();
        this.setupLogout();
        this.setupTools();
        this.setupChat();
        this.setupSecurity();
        this.loadUserData();
        this.startSessionTimer();
    }

    // Tab Navigation System
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');
                
                // Update active states
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                button.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
                
                this.currentTab = tabId;
                this.onTabChange(tabId);
            });
        });
    }

    onTabChange(tabId) {
        switch (tabId) {
            case 'profile':
                this.loadProfileData();
                break;
            case 'security':
                this.loadSecurityStats();
                break;
            case 'chat':
                this.loadChatMessages();
                break;
        }
    }

    // Professional Theme System
    setupThemeSystem() {
        const themeSelector = document.getElementById('theme-selector');
        const container = document.getElementById('dashboard-container');
        
        // Load saved theme
        const savedTheme = localStorage.getItem('inferno-theme') || 'dark';
        themeSelector.value = savedTheme;
        this.applyTheme(savedTheme);

        // Handle theme changes
        themeSelector.addEventListener('change', (e) => {
            const newTheme = e.target.value;
            this.applyTheme(newTheme);
            localStorage.setItem('inferno-theme', newTheme);
        });
    }

    applyTheme(theme) {
        const container = document.getElementById('dashboard-container');
        
        // Remove existing theme classes
        container.classList.remove('theme-dark', 'theme-light', 'theme-auto');
        
        if (theme === 'light') {
            container.classList.add('theme-light');
        } else if (theme === 'auto') {
            // Check system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            container.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
        } else {
            container.classList.add('theme-dark');
        }
    }

    // User Data Management
    async loadUserData() {
        try {
            const response = await fetch('/api/profile');
            const data = await response.json();
            
            if (data.success) {
                this.updateUserInterface(data.profile);
            }
        } catch (error) {
            console.error('Error loading user data:', error);
            this.showNotification('Failed to load user data', 'error');
        }
    }

    updateUserInterface(profile) {
        // Update username and avatar
        const username = profile.username || 'User';
        const firstLetter = username.charAt(0).toUpperCase();
        
        document.getElementById('username').textContent = username;
        document.getElementById('profile-username').textContent = username;
        document.getElementById('nav-user-avatar').textContent = firstLetter;
        document.getElementById('profile-avatar').textContent = firstLetter;
        
        // Update role
        const role = profile.role || 'User';
        document.getElementById('nav-user-role').textContent = role;
        document.getElementById('profile-role').textContent = role;
        
        // Update profile information
        document.getElementById('user-email').textContent = profile.email || 'Not provided';
        document.getElementById('hardware-id').textContent = profile.partial_hwid || 'Loading...';
        
        // Update dates
        if (profile.created_at) {
            const createdDate = new Date(profile.created_at).toLocaleDateString();
            document.getElementById('member-since').textContent = createdDate;
        }
        
        if (profile.last_login) {
            const lastLogin = new Date(profile.last_login).toLocaleString();
            document.getElementById('last-login').textContent = lastLogin;
        }
    }

    // Session Timer
    startSessionTimer() {
        setInterval(() => {
            const elapsed = Date.now() - this.sessionStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const hours = Math.floor(minutes / 60);
            
            let timeString;
            if (hours > 0) {
                timeString = `${hours}h ${minutes % 60}m`;
            } else {
                timeString = `${minutes}m`;
            }
            
            const sessionTimeElement = document.getElementById('session-time');
            if (sessionTimeElement) {
                sessionTimeElement.textContent = timeString;
            }
        }, 60000); // Update every minute
    }

    // Logout System
    setupLogout() {
        const logoutBtn = document.querySelector('[data-action="logout"]');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                try {
                    const response = await fetch('/api/logout', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });
                    
                    if (response.ok) {
                        window.location.href = '/login';
                    }
                } catch (error) {
                    console.error('Logout error:', error);
                }
            });
        }
    }

    // Tools Setup
    setupTools() {
        // IP Analysis
        const analyzeBtn = document.getElementById('analyze-ip');
        const ipInput = document.getElementById('ip-input');
        
        if (analyzeBtn && ipInput) {
            analyzeBtn.addEventListener('click', () => this.analyzeIP());
            ipInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.analyzeIP();
            });
        }

        // User Search
        const searchBtn = document.getElementById('search-user');
        const userInput = document.getElementById('user-input');
        
        if (searchBtn && userInput) {
            searchBtn.addEventListener('click', () => this.searchUser());
            userInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.searchUser();
            });
        }
    }

    async analyzeIP() {
        const ipInput = document.getElementById('ip-input');
        const resultsArea = document.getElementById('ip-results');
        const ip = ipInput.value.trim();
        
        if (!ip) {
            this.showNotification('Please enter an IP address', 'warning');
            return;
        }

        resultsArea.innerHTML = '<div class="loading">Analyzing IP address...</div>';
        
        try {
            const response = await fetch(`/api/security/ip-analysis/${ip}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayIPResults(data, resultsArea);
            } else {
                resultsArea.innerHTML = `<div class="error">Error: ${data.error}</div>`;
            }
        } catch (error) {
            resultsArea.innerHTML = `<div class="error">Connection error: ${error.message}</div>`;
        }
    }

    displayIPResults(data, container) {
        const risk = data.risk_assessment;
        const riskColors = {
            'LOW': '#10b981',
            'MEDIUM': '#f59e0b',
            'HIGH': '#f97316',
            'CRITICAL': '#ef4444'
        };
        
        const riskColor = riskColors[risk.risk_level] || '#6b7280';
        
        container.innerHTML = `
            <div class="ip-analysis-result">
                <div class="result-header">
                    <h4>Analysis Results for ${data.ip}</h4>
                </div>
                <div class="result-content">
                    <div class="result-item">
                        <span class="result-label">Risk Level:</span>
                        <span class="result-value" style="color: ${riskColor}; font-weight: 600;">
                            ${risk.risk_level} (${risk.risk_score})
                        </span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Country:</span>
                        <span class="result-value">${risk.intelligence?.country || 'Unknown'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">ISP:</span>
                        <span class="result-value">${risk.intelligence?.isp || 'Unknown'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Recommendation:</span>
                        <span class="result-value">${risk.recommendation}</span>
                    </div>
                </div>
            </div>
        `;
    }

    searchUser() {
        const userInput = document.getElementById('user-input');
        const resultsArea = document.getElementById('user-results');
        const username = userInput.value.trim();
        
        if (!username) {
            this.showNotification('Please enter a username', 'warning');
            return;
        }

        resultsArea.innerHTML = `
            <div class="user-search-result">
                <div class="result-header">
                    <h4>Search Results for "${username}"</h4>
                </div>
                <div class="result-content">
                    <div class="result-item">
                        <span class="result-label">Status:</span>
                        <span class="result-value">Searching database...</span>
                    </div>
                </div>
            </div>
        `;
    }

    // Chat System
    setupChat() {
        const sendBtn = document.getElementById('send-message');
        const chatInput = document.getElementById('chat-input');
        
        if (sendBtn && chatInput) {
            sendBtn.addEventListener('click', () => this.sendMessage());
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
    }

    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        chatInput.value = '';
        
        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.addChatMessage(data.message);
            }
        } catch (error) {
            this.showNotification('Failed to send message', 'error');
        }
    }

    async loadChatMessages() {
        try {
            const response = await fetch('/api/chat/messages');
            const data = await response.json();
            
            if (data.success) {
                const chatMessages = document.getElementById('chat-messages');
                chatMessages.innerHTML = '';
                
                data.messages.forEach(message => {
                    this.addChatMessage(message);
                });
            }
        } catch (error) {
            console.error('Error loading chat messages:', error);
        }
    }

    addChatMessage(messageData) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        
        const timestamp = new Date(messageData.timestamp * 1000).toLocaleTimeString();
        const avatarLetter = messageData.username.charAt(0).toUpperCase();
        
        messageDiv.className = 'chat-message';
        messageDiv.innerHTML = `
            <div class="message-avatar">${avatarLetter}</div>
            <div class="message-content">
                <div class="message-header">
                    <span class="message-username">${messageData.username}</span>
                    <span class="message-role">${messageData.role}</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-text">${messageData.message}</div>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Security Stats
    async loadSecurityStats() {
        try {
            const response = await fetch('/api/security/stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('blocked-ips').textContent = data.statistics.total_blocked_ips || 0;
                document.getElementById('recent-threats').textContent = data.statistics.recent_threat_events || 0;
            }
        } catch (error) {
            console.error('Error loading security stats:', error);
        }
    }

    // Notification System
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProfessionalDashboard();
});

// Add notification styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    }
    
    .notification-info { background: #3b82f6; }
    .notification-success { background: #10b981; }
    .notification-warning { background: #f59e0b; }
    .notification-error { background: #ef4444; }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(notificationStyles);
