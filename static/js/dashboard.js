// Dashboard JavaScript
'use strict';

// Initialize dashboard
function initializeDashboard() {
    const logoutBtn = document.getElementById('logout-btn');
    const logoutModal = document.getElementById('logout-modal');
    const cancelLogout = document.getElementById('cancel-logout');
    const confirmLogout = document.getElementById('confirm-logout');
    const userName = document.getElementById('user-name');
    
    // Action buttons
    const newMissionBtn = document.getElementById('new-mission-btn');
    const analyticsBtn = document.getElementById('analytics-btn');
    const settingsBtn = document.getElementById('settings-btn');
    const securityBtn = document.getElementById('security-btn');
    
    // Load user info
    loadUserInfo();
    
    // Logout functionality
    if (logoutBtn && logoutModal) {
        logoutBtn.addEventListener('click', function() {
            logoutModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
    }
    
    if (cancelLogout) {
        cancelLogout.addEventListener('click', function() {
            logoutModal.classList.add('hidden');
            document.body.style.overflow = '';
        });
    }
    
    if (confirmLogout) {
        confirmLogout.addEventListener('click', async function() {
            try {
                const response = await window.InfernoSpaceX.api('/logout', {
                    method: 'POST'
                });
                
                if (response.success) {
                    window.InfernoSpaceX.showToast('Выход выполнен', 'success');
                    
                    // Clear sensitive data
                    window.InfernoSpaceX.clearSensitiveData();
                    
                    // Redirect to login
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1000);
                }
            } catch (error) {
                window.InfernoSpaceX.showToast('Ошибка выхода', 'error');
                console.error('Logout error:', error);
            }
        });
    }
    
    // Close modal on background click
    if (logoutModal) {
        logoutModal.addEventListener('click', function(e) {
            if (e.target === logoutModal) {
                logoutModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        });
    }
    
    // Action buttons
    if (newMissionBtn) {
        newMissionBtn.addEventListener('click', function() {
            window.InfernoSpaceX.showToast('🚀 Новая миссия запущена!', 'success');
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }
    
    if (analyticsBtn) {
        analyticsBtn.addEventListener('click', function() {
            window.InfernoSpaceX.showToast('📊 Открываем аналитику...', 'info');
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }
    
    if (settingsBtn) {
        settingsBtn.addEventListener('click', function() {
            window.InfernoSpaceX.showToast('⚙️ Настройки системы', 'info');
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }
    
    if (securityBtn) {
        securityBtn.addEventListener('click', function() {
            window.InfernoSpaceX.showToast('🔒 Мониторинг безопасности активен', 'success');
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    }
    
    // Auto-refresh status
    setInterval(updateSystemStatus, 30000); // Every 30 seconds
}

// Load user information
async function loadUserInfo() {
    try {
        const response = await window.InfernoSpaceX.api('/status');
        
        if (response.authenticated) {
            const userName = document.getElementById('user-name');
            if (userName) {
                userName.textContent = response.username || 'Пользователь';
            }
            
            // Store user info
            window.InfernoSpaceX.state.user = {
                id: response.user_id,
                username: response.username
            };
        } else {
            // Not authenticated, redirect to login
            window.location.href = '/login';
        }
    } catch (error) {
        console.error('Failed to load user info:', error);
        window.InfernoSpaceX.showToast('Ошибка загрузки данных пользователя', 'error');
    }
}

// Update system status
function updateSystemStatus() {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');
    
    if (statusDot && statusText) {
        // Simulate status check
        const isOnline = navigator.onLine;
        
        if (isOnline) {
            statusDot.style.backgroundColor = '#10B981'; // Green
            statusText.textContent = 'Система активна';
        } else {
            statusDot.style.backgroundColor = '#EF4444'; // Red
            statusText.textContent = 'Нет соединения';
        }
    }
}

// Animate stats on load
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach((element, index) => {
        const finalValue = element.textContent;
        
        // Only animate numeric values
        if (!isNaN(finalValue)) {
            const startValue = 0;
            const duration = 2000;
            const startTime = performance.now() + (index * 200); // Stagger animations
            
            function updateNumber(currentTime) {
                if (currentTime < startTime) {
                    requestAnimationFrame(updateNumber);
                    return;
                }
                
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Easing function
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                
                const currentValue = Math.floor(startValue + (finalValue - startValue) * easeOutCubic);
                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = finalValue;
                }
            }
            
            element.textContent = '0';
            requestAnimationFrame(updateNumber);
        }
    });
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Delay animation slightly for better effect
    setTimeout(animateStats, 500);
});
