// Authentication JavaScript
'use strict';

// Initialize login page
function initializeLogin() {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    const passwordToggle = document.getElementById('password-toggle');
    
    // Password toggle
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            window.InfernoSpaceX.handleAction('toggle-password', this);
        });
    }
    
    // Form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (loginBtn.classList.contains('loading')) return;
            
            const formData = new FormData(loginForm);
            const data = {
                username: formData.get('username'),
                password: formData.get('password')
            };
            
            // Validate
            if (!data.username || !data.password) {
                window.InfernoSpaceX.showToast('Заполните все поля', 'error');
                return;
            }
            
            // Show loading
            loginBtn.classList.add('loading');
            loginBtn.querySelector('.btn-text').style.opacity = '0';
            loginBtn.querySelector('.btn-loader').classList.remove('hidden');
            
            try {
                const response = await window.InfernoSpaceX.api('/login', {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                
                if (response.success) {
                    window.InfernoSpaceX.showToast('Вход выполнен успешно!', 'success');
                    
                    // Redirect after short delay
                    setTimeout(() => {
                        window.location.href = response.redirect || '/dashboard';
                    }, 1000);
                } else {
                    window.InfernoSpaceX.showToast(response.message || 'Ошибка входа', 'error');
                }
                
            } catch (error) {
                window.InfernoSpaceX.showToast('Ошибка соединения', 'error');
                console.error('Login error:', error);
            } finally {
                // Hide loading
                loginBtn.classList.remove('loading');
                loginBtn.querySelector('.btn-text').style.opacity = '1';
                loginBtn.querySelector('.btn-loader').classList.add('hidden');
            }
        });
    }
}

// Initialize register page
function initializeRegister() {
    const registerForm = document.getElementById('register-form');
    const registerBtn = document.getElementById('register-btn');
    const passwordToggle = document.getElementById('password-toggle');
    const confirmPasswordToggle = document.getElementById('confirm-password-toggle');
    const termsModal = document.getElementById('terms-modal');
    const termsLink = document.querySelector('.terms-link');
    const closeTerms = document.getElementById('close-terms');
    const acceptTermsBtn = document.getElementById('accept-terms-btn');
    const acceptTermsCheckbox = document.getElementById('accept-terms');
    
    // Password toggles
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            window.InfernoSpaceX.handleAction('toggle-password', this);
        });
    }
    
    if (confirmPasswordToggle) {
        confirmPasswordToggle.addEventListener('click', function() {
            window.InfernoSpaceX.handleAction('toggle-password', this);
        });
    }
    
    // Terms modal
    if (termsLink && termsModal) {
        termsLink.addEventListener('click', function(e) {
            e.preventDefault();
            termsModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
    }
    
    if (closeTerms) {
        closeTerms.addEventListener('click', function() {
            termsModal.classList.add('hidden');
            document.body.style.overflow = '';
        });
    }
    
    if (acceptTermsBtn && acceptTermsCheckbox) {
        acceptTermsBtn.addEventListener('click', function() {
            acceptTermsCheckbox.checked = true;
            termsModal.classList.add('hidden');
            document.body.style.overflow = '';
        });
    }
    
    // Close modal on background click
    if (termsModal) {
        termsModal.addEventListener('click', function(e) {
            if (e.target === termsModal) {
                termsModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        });
    }
    
    // Form validation
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm-password');
    
    if (confirmPasswordInput && passwordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value && this.value !== passwordInput.value) {
                this.setCustomValidity('Пароли не совпадают');
            } else {
                this.setCustomValidity('');
            }
        });
        
        passwordInput.addEventListener('input', function() {
            if (confirmPasswordInput.value && confirmPasswordInput.value !== this.value) {
                confirmPasswordInput.setCustomValidity('Пароли не совпадают');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });
    }
    
    // Form submission
    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (registerBtn.classList.contains('loading')) return;
            
            const formData = new FormData(registerForm);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                'confirm-password': formData.get('confirm-password')
            };
            
            // Validate
            if (!data.username || !data.email || !data.password || !data['confirm-password']) {
                window.InfernoSpaceX.showToast('Заполните все поля', 'error');
                return;
            }
            
            if (data.password !== data['confirm-password']) {
                window.InfernoSpaceX.showToast('Пароли не совпадают', 'error');
                return;
            }
            
            if (!acceptTermsCheckbox.checked) {
                window.InfernoSpaceX.showToast('Необходимо принять условия использования', 'error');
                return;
            }
            
            // Show loading
            registerBtn.classList.add('loading');
            registerBtn.querySelector('.btn-text').style.opacity = '0';
            registerBtn.querySelector('.btn-loader').classList.remove('hidden');
            
            try {
                const response = await window.InfernoSpaceX.api('/register', {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                
                if (response.success) {
                    window.InfernoSpaceX.showToast('Аккаунт создан успешно!', 'success');
                    
                    // Redirect to login after delay
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    window.InfernoSpaceX.showToast(response.message || 'Ошибка регистрации', 'error');
                }
                
            } catch (error) {
                window.InfernoSpaceX.showToast('Ошибка соединения', 'error');
                console.error('Register error:', error);
            } finally {
                // Hide loading
                registerBtn.classList.remove('loading');
                registerBtn.querySelector('.btn-text').style.opacity = '1';
                registerBtn.querySelector('.btn-loader').classList.add('hidden');
            }
        });
    }
}
