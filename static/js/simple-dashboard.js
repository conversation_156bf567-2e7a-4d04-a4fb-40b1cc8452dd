// Simple Dashboard - Только 3 вкладки
class SimpleDashboard {
    constructor() {
        this.currentTab = 'profile';
        this.init();
    }

    init() {
        this.setupTabs();
        this.setupTheme();
        this.setupLogout();
        this.setupChat();
        this.setupLookup();
        this.loadUserData();
    }

    // Переключение вкладок
    setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-button[data-tab]');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');
                
                // Убираем активные классы
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Добавляем активные классы
                button.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
                
                this.currentTab = tabId;
            });
        });
    }

    // Переключение темы
    setupTheme() {
        const themeSelect = document.getElementById('theme-select');
        const savedTheme = localStorage.getItem('theme') || 'dark';
        
        themeSelect.value = savedTheme;
        this.applyTheme(savedTheme);

        themeSelect.addEventListener('change', (e) => {
            const theme = e.target.value;
            this.applyTheme(theme);
            localStorage.setItem('theme', theme);
        });
    }

    applyTheme(theme) {
        const dashboard = document.querySelector('.simple-dashboard');
        dashboard.classList.remove('theme-light', 'theme-dark');
        dashboard.classList.add(`theme-${theme}`);
    }

    // Выход
    setupLogout() {
        const logoutBtn = document.getElementById('logout-btn');
        
        logoutBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    window.location.href = '/login';
                } else {
                    alert('Ошибка выхода');
                }
            } catch (error) {
                console.error('Logout error:', error);
                alert('Ошибка соединения');
            }
        });
    }

    // Чат
    setupChat() {
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        
        const sendMessage = async () => {
            const message = chatInput.value.trim();
            if (!message) return;
            
            // Добавляем сообщение в чат
            this.addMessage('Вы', message);
            chatInput.value = '';
            
            try {
                const response = await fetch('/api/chat/send', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });
                
                if (!response.ok) {
                    this.addMessage('Система', 'Ошибка отправки сообщения');
                }
            } catch (error) {
                this.addMessage('Система', 'Ошибка соединения');
            }
        };

        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });
    }

    addMessage(username, text) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        
        const now = new Date().toLocaleTimeString('ru-RU', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        messageDiv.innerHTML = `
            <div class="message-header">${username} • ${now}</div>
            <div class="message-text">${text}</div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Пробив
    setupLookup() {
        // IP анализ
        const analyzeBtn = document.getElementById('analyze-ip-btn');
        const ipInput = document.getElementById('ip-input');
        const ipResults = document.getElementById('ip-results');
        
        analyzeBtn.addEventListener('click', async () => {
            const ip = ipInput.value.trim();
            if (!ip) {
                ipResults.textContent = 'Введите IP адрес';
                return;
            }
            
            ipResults.textContent = 'Анализируем...';
            
            try {
                const response = await fetch(`/api/security/ip-analysis/${ip}`);
                const data = await response.json();
                
                if (data.success) {
                    const risk = data.risk_assessment;
                    ipResults.innerHTML = `
IP: ${data.ip}
Уровень риска: ${risk.risk_level} (${risk.risk_score})
Страна: ${risk.intelligence?.country || 'Неизвестно'}
ISP: ${risk.intelligence?.isp || 'Неизвестно'}
Рекомендация: ${risk.recommendation}
                    `;
                } else {
                    ipResults.textContent = `Ошибка: ${data.error}`;
                }
            } catch (error) {
                ipResults.textContent = 'Ошибка соединения';
            }
        });

        // Поиск пользователя
        const searchBtn = document.getElementById('search-user-btn');
        const userInput = document.getElementById('user-input');
        const userResults = document.getElementById('user-results');
        
        searchBtn.addEventListener('click', () => {
            const username = userInput.value.trim();
            if (!username) {
                userResults.textContent = 'Введите имя пользователя';
                return;
            }
            
            userResults.innerHTML = `
Поиск: ${username}
Статус: Поиск в базе данных...
Результат: Пользователь не найден или данные скрыты
            `;
        });
    }

    // Загрузка данных пользователя
    async loadUserData() {
        try {
            const response = await fetch('/api/profile');
            const data = await response.json();
            
            if (data.success) {
                const profile = data.profile;
                
                // Обновляем профиль
                document.getElementById('profile-username').textContent = profile.username;
                document.getElementById('profile-role').textContent = profile.role || 'User';
                document.getElementById('hardware-id').textContent = profile.partial_hwid || 'Загрузка...';
                
                if (profile.created_at) {
                    const date = new Date(profile.created_at).toLocaleDateString('ru-RU');
                    document.getElementById('registration-date').textContent = date;
                }
                
                if (profile.last_login) {
                    const date = new Date(profile.last_login).toLocaleString('ru-RU');
                    document.getElementById('last-login').textContent = date;
                }
            }
        } catch (error) {
            console.error('Error loading user data:', error);
        }
    }
}

// Запуск
document.addEventListener('DOMContentLoaded', () => {
    new SimpleDashboard();
});
