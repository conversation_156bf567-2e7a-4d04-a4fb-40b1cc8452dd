#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Supabase RLS (Row Level Security) Issues
Provides SQL commands to disable <PERSON><PERSON> for development
"""

def print_rls_fix_sql():
    """Print SQL commands to fix RLS issues"""
    print("🔧 SUPABASE RLS FIX")
    print("="*80)
    print("❌ Problem: Row Level Security is blocking access to tables")
    print("✅ Solution: Disable RLS for development (or create proper policies)")
    print("="*80)
    
    print("\n📝 COPY AND PASTE THIS SQL INTO YOUR SUPABASE SQL EDITOR:")
    print("🔗 Go to: https://uceidograrzhuwobxdqc.supabase.co/project/uceidograrzhuwobxdqc/sql")
    print("="*80)
    
    sql_commands = """
-- First, create tables if they don't exist
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    role VARCHAR(20) DEFAULT 'user'
);

CREATE TABLE IF NOT EXISTS security_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    severity VARCHAR(20) DEFAULT 'INFO',
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS threat_intelligence (
    id SERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    threat_type VARCHAR(50),
    threat_score INTEGER DEFAULT 0,
    country VARCHAR(2),
    is_malicious BOOLEAN DEFAULT FALSE,
    first_seen TIMESTAMP DEFAULT NOW(),
    last_seen TIMESTAMP DEFAULT NOW(),
    metadata JSONB
);

-- DISABLE RLS FOR DEVELOPMENT (WARNING: Only for development!)
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE security_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE threat_intelligence DISABLE ROW LEVEL SECURITY;

-- Alternative: Create permissive policies (more secure)
-- Uncomment these if you want to keep RLS enabled:

/*
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE threat_intelligence ENABLE ROW LEVEL SECURITY;

-- Create policies that allow all operations for authenticated users
CREATE POLICY "Allow all operations on users" ON users
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on security_events" ON security_events
    FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow all operations on threat_intelligence" ON threat_intelligence
    FOR ALL USING (true) WITH CHECK (true);
*/

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_events_created ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_ip ON threat_intelligence(ip_address);

-- Insert a test user to verify everything works
INSERT INTO users (username, email, password_hash, is_active, role) 
VALUES ('admin', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=4$test', true, 'admin')
ON CONFLICT (username) DO NOTHING;
"""
    
    print(sql_commands)
    print("="*80)
    print("🚨 IMPORTANT NOTES:")
    print("1. This disables RLS for DEVELOPMENT only")
    print("2. For PRODUCTION, use proper RLS policies instead")
    print("3. After running this SQL, your app should work")
    print("="*80)

def print_production_rls_sql():
    """Print production-ready RLS policies"""
    print("\n🔒 PRODUCTION RLS POLICIES (Optional)")
    print("="*80)
    print("For production, use these policies instead of disabling RLS:")
    print("="*80)
    
    production_sql = """
-- Enable RLS (more secure for production)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE threat_intelligence ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own data" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Allow service role to do everything (for your app)
CREATE POLICY "Service role full access users" ON users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access events" ON security_events
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access intel" ON threat_intelligence
    FOR ALL USING (auth.role() = 'service_role');
"""
    
    print(production_sql)
    print("="*80)

if __name__ == "__main__":
    print_rls_fix_sql()
    print_production_rls_sql()
    
    print("\n🚀 NEXT STEPS:")
    print("1. Copy the SQL above")
    print("2. Go to Supabase SQL Editor")
    print("3. Paste and run the SQL")
    print("4. Run: python create_supabase_tables.py (to test)")
    print("5. Run: python app.py (to start the app)")
    print("\n✅ Your InfernoSpaceX will be ready!")
