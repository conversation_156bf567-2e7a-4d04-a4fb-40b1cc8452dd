#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InfernoSpaceX - Secure Authentication System
Military-grade security implementation
"""

import os
import sys
import threading
import webbrowser
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from werkzeug.security import generate_password_hash, check_password_hash
import secrets
import time

# Import our security modules
from core.security.hardware_fingerprint import get_hardware_id
from core.security.anti_analysis import check_security_environment
from core.crypto.hybrid_encryption import encrypt_data, decrypt_data
from core.database.supabase_db import init_database, create_user, authenticate_user, log_security_event
from core.network.fortress_api import setup_security_headers, apply_security_headers
from core.monitoring.threat_detector import AdvancedThreatDetector
from core.monitoring.ml_anomaly_detector import AdvancedBehavioralAnalyzer
from core.monitoring.ip_intelligence import AdvancedIPAnalyzer
from core.monitoring.payload_analyzer import AdvancedPayloadAnalyzer

# Initialize Flask app with security
app = Flask(__name__)
app.secret_key = secrets.token_hex(32)  # Cryptographically secure secret key

# Initialize advanced security systems
print("🔒 Initializing military-grade security systems...")
threat_detector = AdvancedThreatDetector()
behavioral_analyzer = AdvancedBehavioralAnalyzer()
ip_analyzer = AdvancedIPAnalyzer()
payload_analyzer = AdvancedPayloadAnalyzer()
hardware_id = get_hardware_id()

print("✅ Advanced threat detection system online")
print("✅ ML behavioral analysis system online")
print("✅ IP intelligence system online")
print("✅ Advanced payload analyzer online")

# Security check on startup
if not check_security_environment():
    print("⚠️  Security threat detected! Exiting...")
    sys.exit(1)

@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    return apply_security_headers(response)

@app.before_request
def smart_security_check():
    """Smart security checks - only block real threats"""
    ip = request.remote_addr
    user_agent = request.headers.get('User-Agent', '')

    # Skip security checks for static files and safe endpoints
    safe_endpoints = ['static', 'index', 'login', 'register', 'dashboard', 'security_dashboard']
    if request.endpoint in safe_endpoints:
        return None

    # Only do intensive checks for POST/PUT/PATCH requests with data
    if request.method in ['POST', 'PUT', 'PATCH']:
        # Layer 1: Check for obvious attack tools in User-Agent
        attack_tools = ['sqlmap', 'nikto', 'nmap', 'burp', 'w3af', 'skipfish']
        if any(tool in user_agent.lower() for tool in attack_tools):
            threat_detector.log_event("attack_tool_detected", None, ip, user_agent, {
                "user_agent": user_agent,
                "action": "blocked"
            }, "HIGH")
            return jsonify({"error": "Access denied"}), 403

        # Layer 2: Payload Analysis for POST data
        if request.form or request.is_json:
            payload_data = str(request.get_json() if request.is_json else request.form.to_dict())
            payload_analysis = payload_analyzer.analyze_payload(payload_data)

            # Only block if very high threat score
            if payload_analysis['threat_score'] > 25:
                threat_detector.log_event("malicious_payload_detected", None, ip, user_agent, {
                    "payload_analysis": payload_analysis,
                    "action": "blocked"
                }, "CRITICAL")
                return jsonify({"error": "Access denied - Malicious payload"}), 403

    # Layer 3: Check for honeypot access (always check this)
    honeypot_paths = ['/admin', '/wp-admin', '/.env', '/phpmyadmin', '/config.php', '/backup.zip']
    if any(path in request.path for path in honeypot_paths):
        threat_detector.log_event("honeypot_access", None, ip, user_agent, {
            "path": request.path,
            "action": "blocked"
        }, "CRITICAL")
        return jsonify({"error": "Access denied"}), 403

    # Layer 4: Basic rate limiting (more lenient)
    if not threat_detector.ip_rate_limit.is_allowed(ip):
        return jsonify({"error": "Too many requests"}), 429

@app.route('/')
def index():
    """Main page - redirect to login if not authenticated"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login')
def login():
    """Login page"""
    return render_template('login.html')

@app.route('/register')
def register():
    """Registration page"""
    return render_template('register.html')

@app.route('/dashboard')
def dashboard():
    """Главная панель - Профиль, Пробив, Чат"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('main_panel.html',
                         user_id=session['user_id'],
                         username=session.get('username', 'User'))

@app.route('/panel')
def main_panel():
    """Чистая панель - Профиль, Пробив, Чат"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('main_panel.html',
                         user_id=session['user_id'],
                         username=session.get('username', 'User'))


@app.route('/dashboard/midnight')
def dashboard_midnight():
    """Midnight Dashboard (полуночный дизайн)"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html',
                         user_id=session['user_id'],
                         username=session.get('username', 'Командир'))

@app.route('/dashboard/classic')
def dashboard_classic():
    """Classic Dashboard (старая версия)"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('modern_dashboard.html',
                         user_id=session['user_id'],
                         username=session.get('username', 'Unknown'))

@app.route('/security-dashboard')
def security_dashboard():
    """Advanced Security Dashboard - only for authenticated users"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('security_dashboard.html', user_id=session['user_id'])

# Chat API endpoints
@app.route('/api/chat/messages', methods=['GET'])
def get_chat_messages():
    """Get recent chat messages"""
    if 'user_id' not in session:
        return jsonify({"success": False, "error": "Not authenticated"}), 401

    try:
        # Get messages from last 3 hours
        from core.database.supabase_db import db
        import time

        three_hours_ago = time.time() - (3 * 60 * 60)

        # For now, return mock messages - will implement real storage later
        messages = [
            {
                "id": 1,
                "username": "System",
                "role": "Admin",
                "message": "Добро пожаловать в общий чат InfernoSpaceX!",
                "timestamp": time.time(),
                "type": "system"
            }
        ]

        return jsonify({"success": True, "messages": messages})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """Send a chat message"""
    if 'user_id' not in session:
        return jsonify({"success": False, "error": "Not authenticated"}), 401

    try:
        data = request.get_json()
        message = data.get('message', '').strip()

        if not message:
            return jsonify({"success": False, "error": "Message cannot be empty"}), 400

        # Get user info
        username = session.get('username', 'Unknown')
        user_role = session.get('role', 'User')

        # For now, just return the message - will implement real storage later
        response_message = {
            "id": int(time.time()),
            "username": username,
            "role": user_role,
            "message": message,
            "timestamp": time.time(),
            "type": "user"
        }

        return jsonify({"success": True, "message": response_message})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/profile', methods=['GET'])
def get_profile():
    """Get user profile information"""
    if 'user_id' not in session:
        return jsonify({"success": False, "error": "Not authenticated"}), 401

    try:
        from core.database.supabase_db import db
        import hashlib
        import platform

        user_id = session['user_id']
        user = db.get_user_by_id(user_id)

        if not user:
            return jsonify({"success": False, "error": "User not found"}), 404

        # Generate partial HWID (first 8 characters + last 4)
        full_hwid = hashlib.md5(f"{platform.node()}{platform.processor()}".encode()).hexdigest()
        partial_hwid = f"{full_hwid[:8]}...{full_hwid[-4:]}"

        # Determine user role
        role = user.get('role', 'User')
        role_class = f"role-{role.lower()}"

        profile_data = {
            "username": user['username'],
            "email": user.get('email', ''),
            "role": role,
            "role_class": role_class,
            "created_at": user.get('created_at', ''),
            "last_login": user.get('last_login', ''),
            "partial_hwid": partial_hwid,
            "is_active": user.get('is_active', True)
        }

        return jsonify({"success": True, "profile": profile_data})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/register', methods=['POST'])
def api_register():
    """API endpoint for user registration"""
    try:
        data = request.get_json()
        
        # Validate input
        if not data or not all(k in data for k in ('username', 'email', 'password')):
            return jsonify({"success": False, "message": "Missing required fields"}), 400
        
        username = data['username'].strip()
        email = data['email'].strip().lower()
        password = data['password']
        
        # Basic validation
        if len(username) < 3:
            return jsonify({"success": False, "message": "Username must be at least 3 characters"}), 400
        
        if len(password) < 6:
            return jsonify({"success": False, "message": "Password must be at least 6 characters"}), 400
        
        if '@' not in email:
            return jsonify({"success": False, "message": "Invalid email format"}), 400
        
        # Create user with Supabase
        success, message = create_user(username, email, password)

        if success:
            return jsonify({
                "success": True,
                "message": message
            })
        else:
            return jsonify({
                "success": False,
                "message": message
            }), 409
            
    except Exception as e:
        threat_detector.log_event("registration_error", {"error": str(e)})
        return jsonify({"success": False, "message": "Registration failed"}), 500

@app.route('/api/login', methods=['POST'])
def api_login():
    """API endpoint for user authentication"""
    try:
        data = request.get_json()
        
        # Validate input
        if not data or not all(k in data for k in ('username', 'password')):
            return jsonify({"success": False, "message": "Missing credentials"}), 400
        
        username = data['username'].strip()
        password = data['password']
        
        # Get client info for security logging
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')

        # Authenticate user with Supabase
        success, user_data = authenticate_user(username, password, ip_address, user_agent)

        if success:
            # Create secure session
            session['user_id'] = user_data['id']
            session['username'] = user_data['username']
            session['role'] = user_data.get('role', 'user')
            session['login_time'] = time.time()

            return jsonify({
                "success": True,
                "message": "Login successful!",
                "redirect": url_for('dashboard'),
                "user": {
                    "id": user_data['id'],
                    "username": user_data['username'],
                    "role": user_data.get('role', 'user')
                }
            })
        else:
            return jsonify({
                "success": False,
                "message": "Invalid credentials"
            }), 401
            
    except Exception as e:
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        threat_detector.log_event("login_error", None, ip_address, user_agent, {"error": str(e)}, "ERROR")
        return jsonify({"success": False, "message": "Login failed"}), 500

@app.route('/api/logout', methods=['POST'])
def api_logout():
    """API endpoint for user logout"""
    if 'user_id' in session:
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        threat_detector.log_event("user_logout", session['user_id'], ip_address, user_agent, {
            "username": session.get('username', 'unknown')
        }, "INFO")
        session.clear()

    return jsonify({"success": True, "message": "Logged out successfully"})

@app.route('/api/status')
def api_status():
    """API endpoint to check authentication status"""
    if 'user_id' in session:
        return jsonify({
            "authenticated": True,
            "user_id": session['user_id'],
            "username": session['username'],
            "role": session.get('role', 'User')
        })
    else:
        return jsonify({"authenticated": False})

@app.route('/api/security/stats')
def api_security_stats():
    """API endpoint for security statistics (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    # In production, add admin role check here

    try:
        stats = threat_detector.get_advanced_threat_statistics()
        return jsonify({
            "success": True,
            "statistics": stats,
            "timestamp": time.time()
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/security/ip-analysis/<ip>')
def api_ip_analysis(ip):
    """API endpoint for IP analysis (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        # Validate IP format
        import ipaddress
        ipaddress.ip_address(ip)

        # Get comprehensive IP analysis
        risk_assessment = ip_analyzer.get_ip_risk_assessment(ip)
        threat_risk = threat_detector.get_ip_risk_score(ip)

        return jsonify({
            "success": True,
            "ip": ip,
            "risk_assessment": risk_assessment,
            "threat_profile": threat_risk,
            "timestamp": time.time()
        })

    except ValueError:
        return jsonify({"success": False, "error": "Invalid IP address"}), 400
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/security/whitelist-ip', methods=['POST'])
def api_whitelist_ip():
    """API endpoint to whitelist IP (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        data = request.get_json()
        if not data or 'ip' not in data:
            return jsonify({"success": False, "error": "IP address required"}), 400

        ip = data['ip']
        reason = data.get('reason', 'Manual whitelist via API')

        success = threat_detector.whitelist_ip(ip, reason)

        if success:
            return jsonify({
                "success": True,
                "message": f"IP {ip} whitelisted successfully",
                "ip": ip,
                "reason": reason
            })
        else:
            return jsonify({"success": False, "error": "Failed to whitelist IP"}), 400

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/security/block-ip', methods=['POST'])
def api_block_ip():
    """API endpoint to block IP (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        data = request.get_json()
        if not data or 'ip' not in data:
            return jsonify({"success": False, "error": "IP address required"}), 400

        ip = data['ip']
        duration = data.get('duration', 3600)  # Default 1 hour
        reason = data.get('reason', 'Manual block via API')

        success = threat_detector.blacklist_ip(ip, duration, reason)

        if success:
            return jsonify({
                "success": True,
                "message": f"IP {ip} blocked for {duration} seconds",
                "ip": ip,
                "duration": duration,
                "reason": reason
            })
        else:
            return jsonify({"success": False, "error": "Failed to block IP"}), 400

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/security/threat-intelligence')
def api_threat_intelligence():
    """API endpoint to export threat intelligence (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        intelligence = threat_detector.export_threat_intelligence()

        return jsonify({
            "success": True,
            "threat_intelligence": json.loads(intelligence),
            "export_timestamp": time.time()
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/security/analyze-payload', methods=['POST'])
def api_analyze_payload():
    """API endpoint to analyze payload (admin only)"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        data = request.get_json()
        if not data or 'payload' not in data:
            return jsonify({"success": False, "error": "Payload required"}), 400

        payload = data['payload']
        analysis = payload_analyzer.analyze_payload(payload)

        return jsonify({
            "success": True,
            "analysis": analysis,
            "timestamp": time.time()
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/osint/domain/<domain>')
def api_domain_analysis(domain):
    """API endpoint for domain analysis"""
    if 'user_id' not in session:
        return jsonify({"error": "Authentication required"}), 401

    try:
        import socket
        import ssl
        import requests
        from urllib.parse import urlparse

        # Basic domain validation
        if not domain or '.' not in domain:
            return jsonify({"success": False, "error": "Invalid domain"}), 400

        # Clean domain
        domain = domain.lower().strip()
        if domain.startswith('http'):
            domain = urlparse(domain).netloc

        analysis = {
            "domain": domain,
            "timestamp": time.time(),
            "dns_info": {},
            "ssl_info": {},
            "http_info": {},
            "security_info": {}
        }

        # DNS Resolution
        try:
            ip = socket.gethostbyname(domain)
            analysis["dns_info"]["ip"] = ip
            analysis["dns_info"]["resolved"] = True
        except:
            analysis["dns_info"]["resolved"] = False
            analysis["dns_info"]["error"] = "DNS resolution failed"

        # SSL Certificate Check
        try:
            context = ssl.create_default_context()
            with socket.create_connection((domain, 443), timeout=5) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert = ssock.getpeercert()
                    analysis["ssl_info"]["valid"] = True
                    analysis["ssl_info"]["issuer"] = cert.get('issuer', [{}])[0].get('organizationName', 'Unknown')
                    analysis["ssl_info"]["expires"] = cert.get('notAfter', 'Unknown')
        except:
            analysis["ssl_info"]["valid"] = False
            analysis["ssl_info"]["error"] = "SSL check failed"

        # HTTP Response Check
        try:
            response = requests.get(f"http://{domain}", timeout=5, allow_redirects=True)
            analysis["http_info"]["status_code"] = response.status_code
            analysis["http_info"]["accessible"] = True
            analysis["http_info"]["redirects"] = len(response.history) > 0
        except:
            analysis["http_info"]["accessible"] = False
            analysis["http_info"]["error"] = "HTTP check failed"

        # Security Assessment
        analysis["security_info"]["risk_level"] = "Low"
        analysis["security_info"]["reputation"] = "Clean"
        analysis["security_info"]["blacklisted"] = False

        return jsonify({
            "success": True,
            "analysis": analysis,
            "timestamp": time.time()
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

def open_browser():
    """Open browser after a short delay"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """Main function to start the application"""
    print("🚀 Starting InfernoSpaceX...")
    print("🔒 Initializing security systems...")
    
    # Initialize database
    init_database()
    
    print(f"🔑 Hardware ID: {hardware_id[:8]}...")
    print("🌐 Starting web server...")
    
    # Open browser in a separate thread
    threading.Thread(target=open_browser, daemon=True).start()
    
    # Start Flask app
    try:
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,  # Never debug in production
            threaded=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down InfernoSpaceX...")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == '__main__':
    main()
