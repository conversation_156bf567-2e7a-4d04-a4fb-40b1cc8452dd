#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Machine Learning Anomaly Detection Module
Advanced behavioral analysis and pattern recognition
"""

import numpy as np
import json
import time
import hashlib
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, deque
from dataclasses import dataclass
import math
import threading
import pickle
import os

@dataclass
class BehavioralFeatures:
    """Behavioral features for ML analysis"""
    request_frequency: float = 0.0
    payload_entropy: float = 0.0
    endpoint_diversity: float = 0.0
    user_agent_consistency: float = 0.0
    geographic_consistency: float = 0.0
    time_pattern_score: float = 0.0
    payload_size_variance: float = 0.0
    http_method_diversity: float = 0.0
    header_anomaly_score: float = 0.0
    session_duration: float = 0.0

class SimpleAnomalyDetector:
    """Simplified ML-based anomaly detector using statistical methods"""
    
    def __init__(self):
        self.feature_stats = defaultdict(lambda: {'mean': 0, 'std': 1, 'count': 0})
        self.normal_profiles = {}
        self.anomaly_threshold = 2.5  # Standard deviations
        self.learning_rate = 0.1
        self.min_samples = 50
        
    def update_statistics(self, features: BehavioralFeatures):
        """Update running statistics for features"""
        feature_dict = features.__dict__
        
        for feature_name, value in feature_dict.items():
            if isinstance(value, (int, float)):
                stats = self.feature_stats[feature_name]
                
                # Update running mean and std
                stats['count'] += 1
                delta = value - stats['mean']
                stats['mean'] += delta / stats['count']
                
                if stats['count'] > 1:
                    stats['std'] = math.sqrt(
                        ((stats['count'] - 2) * stats['std']**2 + delta**2) / (stats['count'] - 1)
                    )
    
    def calculate_anomaly_score(self, features: BehavioralFeatures) -> float:
        """Calculate anomaly score based on statistical deviation"""
        if not self.feature_stats:
            return 0.0
        
        total_score = 0.0
        feature_count = 0
        
        feature_dict = features.__dict__
        
        for feature_name, value in feature_dict.items():
            if isinstance(value, (int, float)) and feature_name in self.feature_stats:
                stats = self.feature_stats[feature_name]
                
                if stats['count'] >= self.min_samples and stats['std'] > 0:
                    # Calculate z-score
                    z_score = abs(value - stats['mean']) / stats['std']
                    total_score += z_score
                    feature_count += 1
        
        return total_score / max(feature_count, 1)
    
    def is_anomaly(self, features: BehavioralFeatures) -> bool:
        """Determine if features represent an anomaly"""
        score = self.calculate_anomaly_score(features)
        return score > self.anomaly_threshold

class AdvancedBehavioralAnalyzer:
    """Advanced behavioral analysis system"""
    
    def __init__(self):
        self.user_profiles = defaultdict(lambda: {
            'request_times': deque(maxlen=1000),
            'endpoints': defaultdict(int),
            'user_agents': set(),
            'payloads': deque(maxlen=100),
            'countries': set(),
            'methods': defaultdict(int),
            'headers': defaultdict(int),
            'session_start': time.time(),
            'total_requests': 0
        })
        
        self.anomaly_detector = SimpleAnomalyDetector()
        self.baseline_established = False
        self.learning_mode = True
        self.model_file = "data/behavioral_model.pkl"
        
        # Load existing model if available
        self._load_model()
        
        # Background learning thread
        self.learning_thread = threading.Thread(target=self._learning_worker, daemon=True)
        self.learning_thread.start()
    
    def analyze_behavior(self, ip: str, request_data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Analyze behavioral patterns and return anomaly score and detected anomalies"""
        profile = self.user_profiles[ip]
        current_time = time.time()
        
        # Update profile
        profile['request_times'].append(current_time)
        profile['total_requests'] += 1
        
        if 'endpoint' in request_data:
            profile['endpoints'][request_data['endpoint']] += 1
        
        if 'user_agent' in request_data:
            profile['user_agents'].add(request_data['user_agent'])
        
        if 'method' in request_data:
            profile['methods'][request_data['method']] += 1
        
        if 'country' in request_data:
            profile['countries'].add(request_data['country'])
        
        # Store payload for analysis
        payload_str = str(request_data.get('payload', ''))
        profile['payloads'].append(payload_str)
        
        # Extract behavioral features
        features = self._extract_behavioral_features(ip, profile, request_data)
        
        # Calculate anomaly score
        anomaly_score = self.anomaly_detector.calculate_anomaly_score(features)
        
        # Detect specific behavioral anomalies
        anomalies = self._detect_behavioral_anomalies(ip, profile, features)
        
        # Update model if in learning mode
        if self.learning_mode and not self.anomaly_detector.is_anomaly(features):
            self.anomaly_detector.update_statistics(features)
        
        return anomaly_score, anomalies
    
    def _extract_behavioral_features(self, ip: str, profile: Dict, request_data: Dict) -> BehavioralFeatures:
        """Extract behavioral features for analysis"""
        current_time = time.time()
        
        # Request frequency (requests per minute)
        recent_requests = [t for t in profile['request_times'] if current_time - t < 60]
        request_frequency = len(recent_requests)
        
        # Payload entropy
        payload_str = str(request_data.get('payload', ''))
        payload_entropy = self._calculate_entropy(payload_str) if payload_str else 0
        
        # Endpoint diversity (unique endpoints / total requests)
        endpoint_diversity = len(profile['endpoints']) / max(profile['total_requests'], 1)
        
        # User agent consistency (1 - unique_agents / total_requests)
        user_agent_consistency = 1 - (len(profile['user_agents']) / max(profile['total_requests'], 1))
        
        # Geographic consistency
        geographic_consistency = 1 - (len(profile['countries']) / max(profile['total_requests'], 1))
        
        # Time pattern score (regularity of requests)
        time_pattern_score = self._calculate_time_pattern_score(profile['request_times'])
        
        # Payload size variance
        payload_sizes = [len(str(p)) for p in profile['payloads']]
        payload_size_variance = np.var(payload_sizes) if payload_sizes else 0
        
        # HTTP method diversity
        http_method_diversity = len(profile['methods']) / max(profile['total_requests'], 1)
        
        # Header anomaly score (simplified)
        header_anomaly_score = self._calculate_header_anomaly_score(request_data)
        
        # Session duration
        session_duration = current_time - profile['session_start']
        
        return BehavioralFeatures(
            request_frequency=request_frequency,
            payload_entropy=payload_entropy,
            endpoint_diversity=endpoint_diversity,
            user_agent_consistency=user_agent_consistency,
            geographic_consistency=geographic_consistency,
            time_pattern_score=time_pattern_score,
            payload_size_variance=payload_size_variance,
            http_method_diversity=http_method_diversity,
            header_anomaly_score=header_anomaly_score,
            session_duration=session_duration
        )
    
    def _detect_behavioral_anomalies(self, ip: str, profile: Dict, features: BehavioralFeatures) -> List[str]:
        """Detect specific behavioral anomalies"""
        anomalies = []
        
        # High request frequency (более чувствительная детекция)
        if features.request_frequency > 30:  # More than 30 requests per minute
            anomalies.append("HIGH_REQUEST_FREQUENCY")
        elif features.request_frequency > 15:  # Moderate frequency
            anomalies.append("MODERATE_REQUEST_FREQUENCY")
        
        # Unusual payload entropy (might indicate encrypted/encoded payloads)
        if features.payload_entropy > 7.5:
            anomalies.append("HIGH_PAYLOAD_ENTROPY")
        
        # Endpoint scanning behavior
        if features.endpoint_diversity > 0.8 and profile['total_requests'] > 20:
            anomalies.append("ENDPOINT_SCANNING")
        
        # User agent switching (bot behavior)
        if features.user_agent_consistency < 0.5 and profile['total_requests'] > 10:
            anomalies.append("USER_AGENT_SWITCHING")
        
        # Geographic hopping
        if features.geographic_consistency < 0.7 and len(profile['countries']) > 2:
            anomalies.append("GEOGRAPHIC_HOPPING")
        
        # Irregular time patterns (might indicate automated tools)
        if features.time_pattern_score > 0.8:
            anomalies.append("AUTOMATED_TIMING")
        
        # HTTP method abuse
        if features.http_method_diversity > 0.5:
            anomalies.append("HTTP_METHOD_ABUSE")
        
        # Session anomalies
        if features.session_duration > 3600 and features.request_frequency > 30:  # Long session with high activity
            anomalies.append("PERSISTENT_SESSION_ABUSE")

        # Rapid sequential requests (bot-like behavior)
        if profile['total_requests'] > 5:
            recent_times = list(profile['request_times'])[-5:]  # Last 5 requests
            if len(recent_times) >= 5:
                time_diffs = [recent_times[i] - recent_times[i-1] for i in range(1, len(recent_times))]
                avg_diff = sum(time_diffs) / len(time_diffs)
                if avg_diff < 0.1:  # Less than 100ms between requests
                    anomalies.append("RAPID_SEQUENTIAL_REQUESTS")

        # Suspicious payload patterns
        recent_payloads = list(profile['payloads'])[-3:]  # Last 3 payloads
        suspicious_keywords = ['select', 'union', 'script', 'alert', 'eval', 'exec', '../', 'passwd', 'admin']

        payload_suspicion = 0
        for payload_str in recent_payloads:
            payload_lower = str(payload_str).lower()
            for keyword in suspicious_keywords:
                if keyword in payload_lower:
                    payload_suspicion += 1

        if payload_suspicion >= 3:
            anomalies.append("SUSPICIOUS_PAYLOAD_PATTERNS")
        
        return anomalies
    
    def _calculate_entropy(self, data: str) -> float:
        """Calculate Shannon entropy"""
        if not data:
            return 0
        
        # Count character frequencies
        char_counts = defaultdict(int)
        for char in data:
            char_counts[char] += 1
        
        # Calculate entropy
        entropy = 0
        data_len = len(data)
        for count in char_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _calculate_time_pattern_score(self, request_times: deque) -> float:
        """Calculate regularity score of request timing"""
        if len(request_times) < 3:
            return 0
        
        # Calculate intervals between requests
        intervals = []
        times = list(request_times)[-20:]  # Last 20 requests
        
        for i in range(1, len(times)):
            intervals.append(times[i] - times[i-1])
        
        if not intervals:
            return 0
        
        # Calculate coefficient of variation (std/mean)
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        
        if mean_interval == 0:
            return 1.0  # Perfect regularity (suspicious)
        
        cv = std_interval / mean_interval
        
        # Convert to regularity score (0 = irregular, 1 = perfectly regular)
        regularity_score = 1 / (1 + cv)
        
        return regularity_score
    
    def _calculate_header_anomaly_score(self, request_data: Dict) -> float:
        """Calculate header anomaly score"""
        # Simplified header analysis
        score = 0.0
        
        # Check for missing common headers
        common_headers = ['user-agent', 'accept', 'accept-language']
        missing_headers = sum(1 for header in common_headers 
                            if f'header_{header}' not in request_data)
        score += missing_headers * 0.3
        
        # Check for suspicious header values
        user_agent = request_data.get('header_user-agent', '')
        if not user_agent or len(user_agent) < 10:
            score += 0.5
        
        return min(score, 1.0)
    
    def _save_model(self):
        """Save behavioral model to disk"""
        try:
            model_data = {
                'feature_stats': dict(self.anomaly_detector.feature_stats),
                'anomaly_threshold': self.anomaly_detector.anomaly_threshold,
                'baseline_established': self.baseline_established
            }
            
            os.makedirs("data", exist_ok=True)
            with open(self.model_file, 'wb') as f:
                pickle.dump(model_data, f)
                
        except Exception as e:
            print(f"❌ Failed to save behavioral model: {e}")
    
    def _load_model(self):
        """Load behavioral model from disk"""
        try:
            if os.path.exists(self.model_file):
                with open(self.model_file, 'rb') as f:
                    model_data = pickle.load(f)
                
                self.anomaly_detector.feature_stats = defaultdict(
                    lambda: {'mean': 0, 'std': 1, 'count': 0},
                    model_data.get('feature_stats', {})
                )
                self.anomaly_detector.anomaly_threshold = model_data.get('anomaly_threshold', 2.5)
                self.baseline_established = model_data.get('baseline_established', False)
                
                print("✅ Loaded behavioral analysis model")
                
        except Exception as e:
            print(f"❌ Failed to load behavioral model: {e}")
    
    def _learning_worker(self):
        """Background worker for model learning and maintenance"""
        while True:
            try:
                # Save model periodically
                self._save_model()
                
                # Check if we have enough data to establish baseline
                total_samples = sum(stats['count'] for stats in self.anomaly_detector.feature_stats.values())
                if total_samples > 1000 and not self.baseline_established:
                    self.baseline_established = True
                    self.learning_mode = False  # Switch to detection mode
                    print("✅ Behavioral baseline established - switching to detection mode")
                
                time.sleep(300)  # Save every 5 minutes
                
            except Exception as e:
                print(f"❌ Learning worker error: {e}")
                time.sleep(60)

if __name__ == "__main__":
    # Test behavioral analysis
    print("🧠 Testing ML Behavioral Analysis...")
    
    analyzer = AdvancedBehavioralAnalyzer()
    
    # Simulate normal behavior
    print("\n1. Testing Normal Behavior:")
    for i in range(10):
        request_data = {
            'endpoint': '/login' if i % 3 == 0 else '/dashboard',
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method': 'GET',
            'payload': {'username': 'user', 'action': 'view'},
            'country': 'US'
        }
        
        score, anomalies = analyzer.analyze_behavior('192.168.1.10', request_data)
        print(f"   Request {i+1}: Score={score:.2f}, Anomalies={anomalies}")
        time.sleep(0.1)
    
    # Simulate suspicious behavior
    print("\n2. Testing Suspicious Behavior:")
    suspicious_requests = [
        {
            'endpoint': f'/admin{i}',
            'user_agent': f'Bot{i}',
            'method': 'POST',
            'payload': {'cmd': f'cat /etc/passwd{i}'},
            'country': 'RU' if i % 2 else 'CN'
        }
        for i in range(20)
    ]
    
    for i, request_data in enumerate(suspicious_requests):
        score, anomalies = analyzer.analyze_behavior('192.168.1.100', request_data)
        print(f"   Suspicious {i+1}: Score={score:.2f}, Anomalies={anomalies}")
    
    print("\n✅ ML Behavioral Analysis test completed!")
