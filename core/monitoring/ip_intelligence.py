#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP Intelligence and Reputation Module
Advanced IP analysis, geolocation, and threat intelligence
"""

import requests
import json
import time
import hashlib
import threading
from typing import Dict, List, Optional, Set, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, field
import ipaddress
import sqlite3
import os
import re

@dataclass
class IPIntelligence:
    """Comprehensive IP intelligence data"""
    ip: str
    country: Optional[str] = None
    country_code: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    isp: Optional[str] = None
    organization: Optional[str] = None
    asn: Optional[str] = None
    is_tor: bool = False
    is_vpn: bool = False
    is_proxy: bool = False
    is_hosting: bool = False
    is_malicious: bool = False
    reputation_score: int = 0
    threat_types: List[str] = field(default_factory=list)
    last_seen_malicious: Optional[float] = None
    confidence_score: float = 0.0

class ThreatIntelligenceFeeds:
    """Threat intelligence feed manager"""
    
    def __init__(self):
        self.feeds = {
            'malicious_ips': set(),
            'tor_exit_nodes': set(),
            'vpn_providers': set(),
            'hosting_providers': set(),
            'botnet_ips': set(),
            'scanner_ips': set()
        }
        
        self.feed_urls = {
            # In production, these would be real threat intelligence feeds
            'tor_exits': 'https://check.torproject.org/torbulkexitlist',
            'malware_ips': 'https://feodotracker.abuse.ch/downloads/ipblocklist.txt',
            'botnet_ips': 'https://sslbl.abuse.ch/blacklist/sslipblacklist.txt'
        }
        
        self.last_update = {}
        self.update_interval = 3600  # 1 hour
        
        # Start background updater
        self.updater_thread = threading.Thread(target=self._update_worker, daemon=True)
        self.updater_thread.start()
    
    def is_malicious_ip(self, ip: str) -> Tuple[bool, List[str]]:
        """Check if IP is known malicious"""
        threats = []
        
        if ip in self.feeds['malicious_ips']:
            threats.append('KNOWN_MALICIOUS')
        
        if ip in self.feeds['botnet_ips']:
            threats.append('BOTNET')
        
        if ip in self.feeds['scanner_ips']:
            threats.append('SCANNER')
        
        return len(threats) > 0, threats
    
    def is_tor_exit_node(self, ip: str) -> bool:
        """Check if IP is Tor exit node"""
        return ip in self.feeds['tor_exit_nodes']
    
    def is_vpn_provider(self, ip: str) -> bool:
        """Check if IP belongs to VPN provider"""
        return ip in self.feeds['vpn_providers']
    
    def is_hosting_provider(self, ip: str) -> bool:
        """Check if IP belongs to hosting provider"""
        return ip in self.feeds['hosting_providers']
    
    def _update_feeds(self):
        """Update threat intelligence feeds"""
        try:
            # Update Tor exit nodes
            self._update_tor_exits()
            
            # Update malicious IPs (simplified - in production use real feeds)
            self._update_malicious_ips()
            
            # Update hosting providers
            self._update_hosting_providers()
            
            print("✅ Updated threat intelligence feeds")
            
        except Exception as e:
            print(f"❌ Failed to update threat feeds: {e}")
    
    def _update_tor_exits(self):
        """Update Tor exit node list"""
        # Simplified implementation - in production, fetch from official Tor list
        # For demo purposes, we'll use some example IPs
        example_tor_exits = [
            '***************',
            '***************',
            '***************'
        ]
        self.feeds['tor_exit_nodes'].update(example_tor_exits)
    
    def _update_malicious_ips(self):
        """Update malicious IP list"""
        # Simplified implementation - in production, fetch from threat intelligence feeds
        example_malicious = [
            '*************',  # Example malicious IP
            '**********'      # Example scanner IP
        ]
        self.feeds['malicious_ips'].update(example_malicious)
        self.feeds['scanner_ips'].update(example_malicious)
    
    def _update_hosting_providers(self):
        """Update hosting provider IP ranges"""
        # Common hosting provider patterns
        hosting_patterns = [
            r'^23\.', r'^104\.', r'^107\.', r'^162\.', r'^192\.241\.',
            r'^138\.197\.', r'^159\.203\.', r'^165\.227\.', r'^167\.172\.',
            r'^178\.62\.', r'^188\.166\.', r'^206\.189\.'
        ]
        
        # This is simplified - in production, use actual IP ranges from providers
        example_hosting = ['***********', '***********', '***********']
        self.feeds['hosting_providers'].update(example_hosting)
    
    def _update_worker(self):
        """Background worker for updating feeds"""
        while True:
            try:
                self._update_feeds()
                time.sleep(self.update_interval)
            except Exception as e:
                print(f"❌ Feed update worker error: {e}")
                time.sleep(300)  # Wait 5 minutes on error

class AdvancedIPAnalyzer:
    """Advanced IP analysis and intelligence system"""
    
    def __init__(self):
        self.threat_feeds = ThreatIntelligenceFeeds()
        self.ip_cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        self.db_file = "data/ip_intelligence.db"
        
        # Initialize database
        self._init_database()
        
        # Geolocation patterns for basic detection
        self.country_patterns = {
            'US': [r'^(23|104|107|162|192\.241)\.'],
            'RU': [r'^(5\.188|31\.13|37\.139|46\.29|77\.88|85\.143|91\.200|95\.213)\.'],
            'CN': [r'^(1\.2|14\.17|27\.115|36\.110|42\.120|58\.240|61\.135|101\.226|103\.235|106\.11|111\.13|112\.124|113\.105|114\.80|115\.239|117\.121|118\.178|119\.147|120\.52|121\.14|122\.114|123\.125|124\.40|125\.39|140\.205|180\.149|182\.61|183\.60|202\.108|211\.136|218\.30|219\.142|220\.181|221\.179|222\.73|223\.5)\.'],
            'DE': [r'^(46\.4|62\.75|78\.46|85\.10|88\.198|91\.250|136\.243|138\.201|144\.76|148\.251|176\.9|178\.63|188\.40|195\.201|213\.133|217\.160)\.']
        }
    
    def analyze_ip(self, ip: str) -> IPIntelligence:
        """Comprehensive IP analysis"""
        # Check cache first
        cache_key = f"{ip}_{int(time.time() // self.cache_ttl)}"
        if cache_key in self.ip_cache:
            return self.ip_cache[cache_key]
        
        # Create intelligence object
        intel = IPIntelligence(ip=ip)
        
        # Basic IP validation
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # Check if private IP
            if ip_obj.is_private:
                intel.confidence_score = 1.0
                intel.country = 'Private'
                self.ip_cache[cache_key] = intel
                return intel
                
        except ValueError:
            intel.confidence_score = 0.0
            return intel
        
        # Threat intelligence analysis
        is_malicious, threat_types = self.threat_feeds.is_malicious_ip(ip)
        intel.is_malicious = is_malicious
        intel.threat_types = threat_types
        
        if is_malicious:
            intel.reputation_score -= 50
            intel.last_seen_malicious = time.time()
        
        # Tor/VPN/Proxy detection
        intel.is_tor = self.threat_feeds.is_tor_exit_node(ip)
        intel.is_vpn = self.threat_feeds.is_vpn_provider(ip)
        intel.is_hosting = self.threat_feeds.is_hosting_provider(ip)
        
        if intel.is_tor:
            intel.reputation_score -= 20
            intel.threat_types.append('TOR_EXIT')
        
        if intel.is_vpn:
            intel.reputation_score -= 10
            intel.threat_types.append('VPN_PROVIDER')
        
        if intel.is_hosting:
            intel.reputation_score -= 5
            intel.threat_types.append('HOSTING_PROVIDER')
        
        # Basic geolocation (simplified)
        intel.country, intel.country_code = self._detect_country(ip)
        
        # ASN and ISP detection (simplified)
        intel.asn, intel.isp = self._detect_asn_isp(ip)
        
        # Calculate final reputation score
        intel.reputation_score = max(-100, min(100, intel.reputation_score))
        
        # Set confidence score
        intel.confidence_score = self._calculate_confidence(intel)
        
        # Cache result
        self.ip_cache[cache_key] = intel
        
        # Store in database
        self._store_intelligence(intel)
        
        return intel
    
    def _detect_country(self, ip: str) -> Tuple[Optional[str], Optional[str]]:
        """Basic country detection using IP patterns"""
        for country_code, patterns in self.country_patterns.items():
            for pattern in patterns:
                if re.match(pattern, ip):
                    country_names = {
                        'US': 'United States',
                        'RU': 'Russia',
                        'CN': 'China',
                        'DE': 'Germany'
                    }
                    return country_names.get(country_code), country_code
        
        return None, None
    
    def _detect_asn_isp(self, ip: str) -> Tuple[Optional[str], Optional[str]]:
        """Basic ASN and ISP detection"""
        # Simplified ISP detection based on IP ranges
        isp_patterns = {
            r'^23\.': ('AS14061', 'DigitalOcean'),
            r'^104\.': ('AS14061', 'DigitalOcean'),
            r'^138\.197\.': ('AS14061', 'DigitalOcean'),
            r'^8\.8\.': ('AS15169', 'Google'),
            r'^1\.1\.': ('AS13335', 'Cloudflare'),
            r'^77\.88\.': ('AS13238', 'Yandex'),
            r'^46\.29\.': ('AS12389', 'Rostelecom')
        }
        
        for pattern, (asn, isp) in isp_patterns.items():
            if re.match(pattern, ip):
                return asn, isp
        
        return None, None
    
    def _calculate_confidence(self, intel: IPIntelligence) -> float:
        """Calculate confidence score for intelligence data"""
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on available data
        if intel.country:
            confidence += 0.2
        
        if intel.asn:
            confidence += 0.1
        
        if intel.is_malicious:
            confidence += 0.2  # High confidence in threat intelligence
        
        return min(1.0, confidence)
    
    def get_ip_risk_assessment(self, ip: str) -> Dict[str, any]:
        """Get comprehensive risk assessment for IP"""
        intel = self.analyze_ip(ip)
        
        risk_factors = []
        risk_score = 0
        
        # Malicious IP
        if intel.is_malicious:
            risk_score += 50
            risk_factors.extend(intel.threat_types)
        
        # Tor exit node
        if intel.is_tor:
            risk_score += 20
            risk_factors.append('Tor Exit Node')
        
        # VPN provider
        if intel.is_vpn:
            risk_score += 10
            risk_factors.append('VPN Provider')
        
        # Hosting provider
        if intel.is_hosting:
            risk_score += 5
            risk_factors.append('Hosting Provider')
        
        # High-risk countries (simplified example)
        high_risk_countries = ['RU', 'CN', 'KP', 'IR']
        if intel.country_code in high_risk_countries:
            risk_score += 15
            risk_factors.append(f'High-risk country: {intel.country}')
        
        # Determine risk level
        if risk_score >= 50:
            risk_level = 'CRITICAL'
        elif risk_score >= 30:
            risk_level = 'HIGH'
        elif risk_score >= 15:
            risk_level = 'MEDIUM'
        elif risk_score > 0:
            risk_level = 'LOW'
        else:
            risk_level = 'MINIMAL'
        
        return {
            'ip': ip,
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'intelligence': intel.__dict__,
            'recommendation': self._get_recommendation(risk_level, risk_score)
        }
    
    def _get_recommendation(self, risk_level: str, risk_score: int) -> str:
        """Get security recommendation based on risk assessment"""
        recommendations = {
            'CRITICAL': 'BLOCK IMMEDIATELY - High threat detected',
            'HIGH': 'BLOCK - Significant risk factors present',
            'MEDIUM': 'MONITOR CLOSELY - Enhanced logging recommended',
            'LOW': 'CAUTION - Basic monitoring sufficient',
            'MINIMAL': 'ALLOW - No significant risk factors'
        }
        
        return recommendations.get(risk_level, 'UNKNOWN RISK LEVEL')
    
    def _init_database(self):
        """Initialize IP intelligence database"""
        try:
            os.makedirs("data", exist_ok=True)
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ip_intelligence (
                    ip TEXT PRIMARY KEY,
                    country TEXT,
                    country_code TEXT,
                    city TEXT,
                    isp TEXT,
                    asn TEXT,
                    is_tor BOOLEAN,
                    is_vpn BOOLEAN,
                    is_malicious BOOLEAN,
                    reputation_score INTEGER,
                    threat_types TEXT,
                    confidence_score REAL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Failed to initialize IP intelligence database: {e}")
    
    def _store_intelligence(self, intel: IPIntelligence):
        """Store intelligence data in database"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO ip_intelligence 
                (ip, country, country_code, city, isp, asn, is_tor, is_vpn, is_malicious, 
                 reputation_score, threat_types, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                intel.ip, intel.country, intel.country_code, intel.city,
                intel.isp, intel.asn, intel.is_tor, intel.is_vpn, intel.is_malicious,
                intel.reputation_score, json.dumps(intel.threat_types), intel.confidence_score
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Failed to store IP intelligence: {e}")

if __name__ == "__main__":
    # Test IP intelligence system
    print("🌍 Testing IP Intelligence System...")
    
    analyzer = AdvancedIPAnalyzer()
    
    # Test various IP addresses
    test_ips = [
        '*******',          # Google DNS
        '*******',          # Cloudflare DNS
        '***********',      # Private IP
        '***********',      # Hosting provider
        '*********',        # Yandex (Russia)
        '*************'     # Example malicious IP
    ]
    
    print("\nIP Risk Assessments:")
    print("-" * 80)
    
    for ip in test_ips:
        assessment = analyzer.get_ip_risk_assessment(ip)
        print(f"\nIP: {ip}")
        print(f"Risk Level: {assessment['risk_level']} (Score: {assessment['risk_score']})")
        print(f"Country: {assessment['intelligence'].get('country', 'Unknown')}")
        print(f"ISP: {assessment['intelligence'].get('isp', 'Unknown')}")
        print(f"Risk Factors: {', '.join(assessment['risk_factors']) if assessment['risk_factors'] else 'None'}")
        print(f"Recommendation: {assessment['recommendation']}")
        print("-" * 40)
    
    print("\n✅ IP Intelligence System test completed!")
