#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Payload Analysis Module
Deep payload inspection, evasion detection, and content analysis
"""

import re
import base64
import urllib.parse
import html
import json
import hashlib
import zlib
import binascii
from typing import Dict, List, Tuple, Optional, Set, Any
from collections import Counter, defaultdict
import unicodedata
import math
import ast

class PayloadNormalizer:
    """Advanced payload normalization to defeat evasion techniques"""
    
    def __init__(self):
        # Compile regex patterns for performance
        self.url_decode_pattern = re.compile(r'%[0-9a-fA-F]{2}')
        self.html_entity_pattern = re.compile(r'&(?:#(?:x[0-9a-fA-F]+|\d+)|[a-zA-Z][a-zA-Z0-9]*);')
        self.unicode_escape_pattern = re.compile(r'\\u[0-9a-fA-F]{4}')
        self.hex_escape_pattern = re.compile(r'\\x[0-9a-fA-F]{2}')
        self.octal_escape_pattern = re.compile(r'\\[0-7]{1,3}')
        
        # SQL comment patterns
        self.sql_comment_patterns = [
            re.compile(r'/\*.*?\*/', re.DOTALL),
            re.compile(r'--.*$', re.MULTILINE),
            re.compile(r'#.*$', re.MULTILINE)
        ]
        
        # Whitespace normalization
        self.whitespace_pattern = re.compile(r'\s+')
        
    def normalize_payload(self, payload: str) -> Dict[str, str]:
        """Comprehensive payload normalization"""
        if not payload:
            return {'original': payload, 'normalized': payload}
        
        normalized = payload
        transformations = []
        
        # 1. URL decoding (multiple layers)
        for i in range(5):  # Handle up to 5 layers of encoding
            decoded = urllib.parse.unquote(normalized)
            if decoded == normalized:
                break
            normalized = decoded
            transformations.append(f'url_decode_layer_{i+1}')
        
        # 2. HTML entity decoding
        if self.html_entity_pattern.search(normalized):
            normalized = html.unescape(normalized)
            transformations.append('html_decode')
        
        # 3. Unicode normalization
        try:
            normalized = unicodedata.normalize('NFKC', normalized)
            transformations.append('unicode_normalize')
        except:
            pass
        
        # 4. Unicode escape decoding
        if self.unicode_escape_pattern.search(normalized):
            try:
                normalized = normalized.encode().decode('unicode_escape')
                transformations.append('unicode_escape_decode')
            except:
                pass
        
        # 5. Hex escape decoding
        if self.hex_escape_pattern.search(normalized):
            try:
                normalized = bytes(normalized, 'utf-8').decode('unicode_escape')
                transformations.append('hex_escape_decode')
            except:
                pass
        
        # 6. Base64 detection and decoding
        base64_decoded = self._try_base64_decode(normalized)
        if base64_decoded:
            normalized = base64_decoded
            transformations.append('base64_decode')
        
        # 7. Remove SQL comments
        for pattern in self.sql_comment_patterns:
            if pattern.search(normalized):
                normalized = pattern.sub('', normalized)
                transformations.append('sql_comment_removal')
        
        # 8. Normalize whitespace
        normalized = self.whitespace_pattern.sub(' ', normalized).strip()
        transformations.append('whitespace_normalize')
        
        # 9. Case normalization for analysis
        normalized_lower = normalized.lower()
        
        return {
            'original': payload,
            'normalized': normalized,
            'normalized_lower': normalized_lower,
            'transformations': transformations
        }
    
    def _try_base64_decode(self, data: str) -> Optional[str]:
        """Attempt to decode base64 data"""
        # Check if string looks like base64
        if len(data) < 4 or len(data) % 4 != 0:
            return None
        
        if not re.match(r'^[A-Za-z0-9+/]*={0,2}$', data):
            return None
        
        try:
            decoded = base64.b64decode(data).decode('utf-8', errors='ignore')
            # Only return if decoded data looks meaningful
            if len(decoded) > 0 and any(c.isprintable() for c in decoded):
                return decoded
        except:
            pass
        
        return None

class AdvancedPayloadAnalyzer:
    """Advanced payload analysis with deep inspection capabilities"""
    
    def __init__(self):
        self.normalizer = PayloadNormalizer()
        
        # Advanced SQL injection patterns
        self.sql_patterns = {
            'union_based': [
                re.compile(r'\bunion\s+(?:all\s+)?select\b', re.IGNORECASE),
                re.compile(r'\bunion\s*/\*.*?\*/\s*select\b', re.IGNORECASE | re.DOTALL),
                re.compile(r'\bunion\s+select\s+null', re.IGNORECASE)
            ],
            'boolean_based': [
                re.compile(r'\b(?:and|or)\s+\d+\s*[=<>]\s*\d+', re.IGNORECASE),
                re.compile(r'\b(?:and|or)\s+\w+\s+like\s+', re.IGNORECASE),
                re.compile(r'\b(?:and|or)\s+\d+\s*(?:=|<>)\s*\d+', re.IGNORECASE)
            ],
            'time_based': [
                re.compile(r'\bwaitfor\s+delay\s+', re.IGNORECASE),
                re.compile(r'\bbenchmark\s*\(\s*\d+', re.IGNORECASE),
                re.compile(r'\bsleep\s*\(\s*\d+', re.IGNORECASE),
                re.compile(r'\bpg_sleep\s*\(\s*\d+', re.IGNORECASE)
            ],
            'error_based': [
                re.compile(r'\bextractvalue\s*\(', re.IGNORECASE),
                re.compile(r'\bupdatexml\s*\(', re.IGNORECASE),
                re.compile(r'\bexp\s*\(\s*~', re.IGNORECASE)
            ],
            'stacked_queries': [
                re.compile(r';\s*(?:drop|create|alter|insert|update|delete)\s+', re.IGNORECASE),
                re.compile(r';\s*exec\s*\(', re.IGNORECASE),
                re.compile(r';\s*xp_cmdshell', re.IGNORECASE)
            ]
        }
        
        # Advanced XSS patterns
        self.xss_patterns = {
            'script_based': [
                re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
                re.compile(r'<script[^>]*>', re.IGNORECASE),
                re.compile(r'javascript\s*:', re.IGNORECASE)
            ],
            'event_based': [
                re.compile(r'\bon\w+\s*=', re.IGNORECASE),
                re.compile(r'\bon(?:load|error|click|mouseover|focus|blur)\s*=', re.IGNORECASE)
            ],
            'dom_based': [
                re.compile(r'\bdocument\.\w+', re.IGNORECASE),
                re.compile(r'\bwindow\.\w+', re.IGNORECASE),
                re.compile(r'\beval\s*\(', re.IGNORECASE)
            ],
            'svg_based': [
                re.compile(r'<svg[^>]*>.*?</svg>', re.IGNORECASE | re.DOTALL),
                re.compile(r'<svg[^>]*onload', re.IGNORECASE)
            ]
        }
        
        # Command injection patterns
        self.command_patterns = {
            'shell_metacharacters': [
                re.compile(r'[;&|`$]\s*\w+', re.IGNORECASE),
                re.compile(r'\$\([^)]+\)', re.IGNORECASE),
                re.compile(r'`[^`]+`', re.IGNORECASE)
            ],
            'system_commands': [
                re.compile(r'\b(?:cat|ls|dir|pwd|whoami|id|uname|ps|netstat|ifconfig)\b', re.IGNORECASE),
                re.compile(r'\b(?:wget|curl|nc|netcat|telnet|ssh)\b', re.IGNORECASE),
                re.compile(r'\b(?:chmod|chown|rm|mv|cp|mkdir|rmdir)\b', re.IGNORECASE)
            ],
            'shell_execution': [
                re.compile(r'/bin/(?:sh|bash|csh|tcsh|zsh)', re.IGNORECASE),
                re.compile(r'cmd\.exe|powershell\.exe', re.IGNORECASE),
                re.compile(r'system\s*\(', re.IGNORECASE)
            ]
        }
        
        # Path traversal patterns
        self.path_patterns = [
            re.compile(r'\.\.[\\/]', re.IGNORECASE),
            re.compile(r'\.\.%2f|\.\.%5c', re.IGNORECASE),
            re.compile(r'%2e%2e%2f|%2e%2e%5c', re.IGNORECASE),
            re.compile(r'/etc/(?:passwd|shadow|hosts|group)', re.IGNORECASE),
            re.compile(r'c:\\(?:windows|winnt)\\system32', re.IGNORECASE)
        ]
    
    def analyze_payload(self, payload: str) -> Dict[str, Any]:
        """Comprehensive payload analysis"""
        if not payload:
            return {'threat_score': 0, 'threats': [], 'analysis': {}}
        
        # Normalize payload
        normalized_data = self.normalizer.normalize_payload(payload)
        normalized = normalized_data['normalized_lower']
        
        analysis = {
            'original_length': len(payload),
            'normalized_length': len(normalized),
            'transformations': normalized_data['transformations'],
            'entropy': self._calculate_entropy(payload),
            'character_distribution': self._analyze_character_distribution(payload),
            'encoding_layers': len([t for t in normalized_data['transformations'] if 'decode' in t])
        }
        
        threats = []
        threat_score = 0
        
        # SQL Injection Analysis
        sql_threats, sql_score = self._analyze_sql_injection(normalized)
        threats.extend(sql_threats)
        threat_score += sql_score
        
        # XSS Analysis
        xss_threats, xss_score = self._analyze_xss(normalized)
        threats.extend(xss_threats)
        threat_score += xss_score
        
        # Command Injection Analysis
        cmd_threats, cmd_score = self._analyze_command_injection(normalized)
        threats.extend(cmd_threats)
        threat_score += cmd_score
        
        # Path Traversal Analysis
        path_threats, path_score = self._analyze_path_traversal(normalized)
        threats.extend(path_threats)
        threat_score += path_score
        
        # Evasion Technique Analysis
        evasion_score = self._analyze_evasion_techniques(normalized_data)
        threat_score += evasion_score
        
        if evasion_score > 0:
            threats.append('EVASION_TECHNIQUES')
        
        # Suspicious Pattern Analysis
        suspicious_score = self._analyze_suspicious_patterns(normalized)
        threat_score += suspicious_score
        
        if suspicious_score > 0:
            threats.append('SUSPICIOUS_PATTERNS')
        
        return {
            'threat_score': threat_score,
            'threats': list(set(threats)),  # Remove duplicates
            'analysis': analysis,
            'normalized_payload': normalized_data['normalized'],
            'confidence': self._calculate_confidence(analysis, threat_score)
        }
    
    def _analyze_sql_injection(self, payload: str) -> Tuple[List[str], int]:
        """Analyze SQL injection patterns"""
        threats = []
        score = 0
        
        for category, patterns in self.sql_patterns.items():
            for pattern in patterns:
                if pattern.search(payload):
                    threats.append(f'SQL_{category.upper()}')
                    score += 15 if category in ['union_based', 'stacked_queries'] else 10
                    break
        
        # Additional SQL keyword analysis
        sql_keywords = ['select', 'insert', 'update', 'delete', 'drop', 'create', 'alter', 'exec']
        keyword_count = sum(1 for keyword in sql_keywords if keyword in payload)
        
        if keyword_count >= 3:
            threats.append('SQL_MULTIPLE_KEYWORDS')
            score += 8
        
        return threats, score
    
    def _analyze_xss(self, payload: str) -> Tuple[List[str], int]:
        """Analyze XSS patterns"""
        threats = []
        score = 0
        
        for category, patterns in self.xss_patterns.items():
            for pattern in patterns:
                if pattern.search(payload):
                    threats.append(f'XSS_{category.upper()}')
                    score += 12 if category in ['script_based', 'dom_based'] else 8
                    break
        
        # Check for encoded XSS
        if any(encoded in payload for encoded in ['%3c', '%3e', '&lt;', '&gt;']):
            threats.append('XSS_ENCODED')
            score += 6
        
        return threats, score
    
    def _analyze_command_injection(self, payload: str) -> Tuple[List[str], int]:
        """Analyze command injection patterns"""
        threats = []
        score = 0
        
        for category, patterns in self.command_patterns.items():
            for pattern in patterns:
                if pattern.search(payload):
                    threats.append(f'CMD_{category.upper()}')
                    score += 15 if category == 'shell_execution' else 10
                    break
        
        return threats, score
    
    def _analyze_path_traversal(self, payload: str) -> Tuple[List[str], int]:
        """Analyze path traversal patterns"""
        threats = []
        score = 0
        
        for pattern in self.path_patterns:
            if pattern.search(payload):
                threats.append('PATH_TRAVERSAL')
                score += 12
                break
        
        return threats, score
    
    def _analyze_evasion_techniques(self, normalized_data: Dict[str, str]) -> int:
        """Analyze evasion techniques used"""
        score = 0
        transformations = normalized_data['transformations']
        
        # Multiple encoding layers
        decode_count = len([t for t in transformations if 'decode' in t])
        if decode_count > 2:
            score += decode_count * 3
        
        # SQL comment evasion
        if 'sql_comment_removal' in transformations:
            score += 5
        
        # Base64 encoding
        if 'base64_decode' in transformations:
            score += 4
        
        return score
    
    def _analyze_suspicious_patterns(self, payload: str) -> int:
        """Analyze general suspicious patterns"""
        score = 0
        
        # High entropy (might indicate obfuscation)
        entropy = self._calculate_entropy(payload)
        if entropy > 7.5:
            score += 5
        
        # Excessive special characters
        special_char_ratio = sum(1 for c in payload if not c.isalnum()) / max(len(payload), 1)
        if special_char_ratio > 0.5:
            score += 3
        
        # Very long payload
        if len(payload) > 1000:
            score += 2
        
        # Null bytes
        if '\x00' in payload:
            score += 8
        
        return score
    
    def _calculate_entropy(self, data: str) -> float:
        """Calculate Shannon entropy"""
        if not data:
            return 0
        
        char_counts = Counter(data)
        data_len = len(data)
        
        entropy = 0
        for count in char_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def _analyze_character_distribution(self, data: str) -> Dict[str, float]:
        """Analyze character distribution"""
        if not data:
            return {}
        
        total = len(data)
        
        return {
            'alphanumeric_ratio': sum(1 for c in data if c.isalnum()) / total,
            'special_char_ratio': sum(1 for c in data if not c.isalnum() and not c.isspace()) / total,
            'whitespace_ratio': sum(1 for c in data if c.isspace()) / total,
            'uppercase_ratio': sum(1 for c in data if c.isupper()) / total,
            'digit_ratio': sum(1 for c in data if c.isdigit()) / total
        }
    
    def _calculate_confidence(self, analysis: Dict[str, Any], threat_score: int) -> float:
        """Calculate confidence in threat detection"""
        confidence = 0.5  # Base confidence
        
        # Higher confidence with more transformations detected
        if analysis['encoding_layers'] > 0:
            confidence += 0.2
        
        # Higher confidence with higher threat score
        if threat_score > 20:
            confidence += 0.3
        elif threat_score > 10:
            confidence += 0.2
        
        # Lower confidence with very high entropy (might be legitimate encrypted data)
        if analysis['entropy'] > 8.0:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))

if __name__ == "__main__":
    # Test payload analyzer
    print("🔍 Testing Advanced Payload Analyzer...")
    
    analyzer = AdvancedPayloadAnalyzer()
    
    # Test payloads
    test_payloads = [
        # SQL Injection
        "admin'; DROP TABLE users; --",
        "1' UNION SELECT username, password FROM users--",
        "1; WAITFOR DELAY '00:00:05'--",
        
        # XSS
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>",
        
        # Command Injection
        "; cat /etc/passwd",
        "| whoami",
        "$(cat /etc/passwd)",
        
        # Path Traversal
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        
        # Encoded payloads
        "%3Cscript%3Ealert('xss')%3C/script%3E",
        "YWRtaW4nOyBEUk9QIFRBQkxFIHVzZXJzOyAtLQ==",  # Base64 encoded SQL
        
        # Normal payload
        "username=admin&password=secret123"
    ]
    
    print("\nPayload Analysis Results:")
    print("=" * 80)
    
    for i, payload in enumerate(test_payloads, 1):
        result = analyzer.analyze_payload(payload)
        
        print(f"\nTest {i}: {payload[:50]}{'...' if len(payload) > 50 else ''}")
        print(f"Threat Score: {result['threat_score']}")
        print(f"Threats: {', '.join(result['threats']) if result['threats'] else 'None'}")
        print(f"Confidence: {result['confidence']:.2f}")
        print(f"Entropy: {result['analysis']['entropy']:.2f}")
        print(f"Transformations: {', '.join(result['analysis']['transformations'])}")
        print("-" * 40)
    
    print("\n✅ Advanced Payload Analyzer test completed!")
