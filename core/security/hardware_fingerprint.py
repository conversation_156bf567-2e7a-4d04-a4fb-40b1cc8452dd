#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hardware Fingerprinting Module
Generates unique hardware-based identifier for device binding
"""

import hashlib
import platform
import subprocess
import uuid
import psutil
import os
from typing import Optional

def get_cpu_info() -> str:
    """Get CPU information"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(
                ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                capture_output=True, text=True, timeout=5
            )
            for line in result.stdout.split('\n'):
                if 'ProcessorId=' in line:
                    return line.split('=')[1].strip()
        else:
            # Linux/Mac fallback
            with open('/proc/cpuinfo', 'r') as f:
                for line in f:
                    if 'processor' in line:
                        return line.split(':')[1].strip()
    except:
        pass
    return platform.processor()

def get_motherboard_serial() -> str:
    """Get motherboard serial number"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(
                ['wmic', 'baseboard', 'get', 'serialnumber', '/value'],
                capture_output=True, text=True, timeout=5
            )
            for line in result.stdout.split('\n'):
                if 'SerialNumber=' in line:
                    return line.split('=')[1].strip()
    except:
        pass
    return "unknown_motherboard"

def get_disk_serial() -> str:
    """Get primary disk serial number"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(
                ['wmic', 'diskdrive', 'get', 'serialnumber', '/value'],
                capture_output=True, text=True, timeout=5
            )
            for line in result.stdout.split('\n'):
                if 'SerialNumber=' in line and line.split('=')[1].strip():
                    return line.split('=')[1].strip()
    except:
        pass
    return "unknown_disk"

def get_mac_addresses() -> str:
    """Get MAC addresses of network interfaces"""
    try:
        mac_addresses = []
        for interface, addrs in psutil.net_if_addrs().items():
            for addr in addrs:
                if addr.family == psutil.AF_LINK:  # MAC address
                    mac_addresses.append(addr.address)
        return '|'.join(sorted(mac_addresses))
    except:
        return str(uuid.getnode())

def get_system_info() -> str:
    """Get system information"""
    try:
        return f"{platform.system()}_{platform.release()}_{platform.machine()}"
    except:
        return "unknown_system"

def get_memory_info() -> str:
    """Get memory information"""
    try:
        memory = psutil.virtual_memory()
        return str(memory.total)
    except:
        return "unknown_memory"

def get_hardware_id() -> str:
    """
    Generate unique hardware fingerprint
    Combines multiple hardware identifiers for maximum uniqueness
    """
    try:
        # Collect hardware identifiers
        identifiers = [
            get_cpu_info(),
            get_motherboard_serial(),
            get_disk_serial(),
            get_mac_addresses(),
            get_system_info(),
            get_memory_info(),
            str(uuid.uuid4()),  # Add some randomness
        ]
        
        # Create composite identifier
        composite = '|'.join(str(id) for id in identifiers if id)
        
        # Generate SHA-256 hash
        hardware_hash = hashlib.sha256(composite.encode('utf-8')).hexdigest()
        
        return hardware_hash
        
    except Exception as e:
        # Fallback to basic system info
        fallback = f"{platform.node()}_{uuid.getnode()}_{platform.system()}"
        return hashlib.sha256(fallback.encode('utf-8')).hexdigest()

def verify_hardware_id(stored_id: str) -> bool:
    """
    Verify if current hardware matches stored ID
    """
    current_id = get_hardware_id()
    return current_id == stored_id

if __name__ == "__main__":
    # Test the hardware fingerprinting
    hw_id = get_hardware_id()
    print(f"Hardware ID: {hw_id}")
    print(f"Length: {len(hw_id)}")
    print(f"Verification: {verify_hardware_id(hw_id)}")
