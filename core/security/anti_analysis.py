#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Anti-Analysis Module
Detects debugging, virtualization, and analysis environments
"""

import os
import sys
import time
import psutil
import platform
import subprocess
from typing import List

# Known debugger processes
DEBUGGER_PROCESSES = [
    'ollydbg.exe', 'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
    'windbg.exe', 'x32dbg.exe', 'x64dbg.exe', 'immunity debugger.exe',
    'cheat engine.exe', 'processhacker.exe', 'procmon.exe', 'procexp.exe',
    'wireshark.exe', 'fiddler.exe', 'httpdebugger.exe', 'charles.exe'
]

# Known VM processes and services
VM_PROCESSES = [
    'vmware.exe', 'vmtoolsd.exe', 'vboxservice.exe', 'vboxtray.exe',
    'sandboxiedcomlaunch.exe', 'sandboxierpcss.exe', 'vmwareuser.exe',
    'vmwareservice.exe', 'vmusrvc.exe', 'prl_cc.exe', 'prl_tools.exe'
]

# VM registry keys (Windows)
VM_REGISTRY_KEYS = [
    r'HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware Tools',
    r'HKEY_LOCAL_MACHINE\SOFTWARE\Oracle\VirtualBox Guest Additions',
    r'HKEY_LOCAL_MACHINE\SYSTEM\ControlSet001\Services\VBoxGuest',
    r'HKEY_LOCAL_MACHINE\SYSTEM\ControlSet001\Services\VMTools'
]

def check_debugger_processes() -> bool:
    """Check for known debugger processes"""
    try:
        running_processes = [proc.name().lower() for proc in psutil.process_iter(['name'])]
        
        for debugger in DEBUGGER_PROCESSES:
            if debugger.lower() in running_processes:
                return True
        return False
    except:
        return False

def check_vm_processes() -> bool:
    """Check for VM-related processes"""
    try:
        running_processes = [proc.name().lower() for proc in psutil.process_iter(['name'])]
        
        for vm_proc in VM_PROCESSES:
            if vm_proc.lower() in running_processes:
                return True
        return False
    except:
        return False

def check_vm_artifacts() -> bool:
    """Check for VM artifacts in system"""
    try:
        # Check system manufacturer
        system_info = platform.uname()
        vm_indicators = ['vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'parallels']
        
        system_str = f"{system_info.system} {system_info.node} {system_info.machine}".lower()
        
        for indicator in vm_indicators:
            if indicator in system_str:
                return True
        
        # Check for VM-specific hardware
        if platform.system() == "Windows":
            try:
                result = subprocess.run(
                    ['wmic', 'computersystem', 'get', 'manufacturer', '/value'],
                    capture_output=True, text=True, timeout=5
                )
                manufacturer = result.stdout.lower()
                for indicator in vm_indicators:
                    if indicator in manufacturer:
                        return True
            except:
                pass
        
        return False
    except:
        return False

def check_timing_attack() -> bool:
    """Detect timing-based analysis (debuggers slow down execution)"""
    try:
        start_time = time.perf_counter()
        
        # Simple operation that should be fast
        for i in range(1000):
            x = i * 2
        
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        # If execution took too long, might be under analysis
        return execution_time > 0.01  # 10ms threshold
    except:
        return False

def check_memory_analysis() -> bool:
    """Check for memory analysis tools"""
    try:
        # Check if process memory is being accessed unusually
        current_process = psutil.Process()
        memory_info = current_process.memory_info()
        
        # Check for unusual memory patterns
        if memory_info.rss > 100 * 1024 * 1024:  # More than 100MB for a simple app
            return True
        
        return False
    except:
        return False

def check_sandbox_environment() -> bool:
    """Check for sandbox environment indicators"""
    try:
        # Check for limited execution time (sandboxes often have timeouts)
        uptime = time.time() - psutil.boot_time()
        if uptime < 300:  # Less than 5 minutes uptime
            return True
        
        # Check for limited disk space (sandboxes often have small disks)
        if platform.system() == "Windows":
            disk_usage = psutil.disk_usage('C:')
            if disk_usage.total < 50 * 1024 * 1024 * 1024:  # Less than 50GB
                return True
        
        # Check for limited RAM
        memory = psutil.virtual_memory()
        if memory.total < 2 * 1024 * 1024 * 1024:  # Less than 2GB
            return True
        
        return False
    except:
        return False

def check_analysis_tools() -> bool:
    """Check for various analysis tools"""
    try:
        # Check for network monitoring tools
        network_tools = ['wireshark', 'tcpdump', 'netstat', 'netmon']
        running_processes = [proc.name().lower() for proc in psutil.process_iter(['name'])]
        
        for tool in network_tools:
            if any(tool in proc for proc in running_processes):
                return True
        
        return False
    except:
        return False

def check_security_environment() -> bool:
    """
    Main security check function
    Returns True if environment is safe, False if threats detected
    """
    try:
        threat_detected = False
        
        # Run all security checks
        checks = [
            ("Debugger processes", check_debugger_processes),
            ("VM processes", check_vm_processes),
            ("VM artifacts", check_vm_artifacts),
            ("Timing analysis", check_timing_attack),
            ("Memory analysis", check_memory_analysis),
            ("Sandbox environment", check_sandbox_environment),
            ("Analysis tools", check_analysis_tools)
        ]
        
        for check_name, check_func in checks:
            try:
                if check_func():
                    print(f"⚠️  Security threat detected: {check_name}")
                    threat_detected = True
            except Exception as e:
                # If a check fails, assume it's suspicious
                print(f"⚠️  Security check failed: {check_name} - {e}")
                threat_detected = True
        
        if not threat_detected:
            print("✅ Security environment check passed")
        
        return not threat_detected
        
    except Exception as e:
        print(f"❌ Security check error: {e}")
        return False

if __name__ == "__main__":
    # Test the anti-analysis system
    print("🔒 Running security environment check...")
    is_safe = check_security_environment()
    print(f"Environment is {'SAFE' if is_safe else 'COMPROMISED'}")
