#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fortress API Security Module
Advanced security headers and API protection
"""

from flask import request, g
import time
import hashlib
from typing import Dict, Any
from collections import defaultdict

# Rate limiting storage
rate_limit_storage = defaultdict(list)
blocked_ips = set()

def setup_security_headers():
    """Setup comprehensive security headers"""
    from flask import current_app

    @current_app.after_request
    def add_security_headers(response):
        # Prevent clickjacking
        response.headers['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # XSS protection
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer policy
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Content Security Policy
        response.headers['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        # HSTS (if using HTTPS)
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Permissions policy
        response.headers['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        # Cache control for sensitive pages
        if request.endpoint in ['login', 'register', 'dashboard']:
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        
        return response

def apply_security_headers(response):
    """Apply security headers to response"""
    # Prevent clickjacking
    response.headers['X-Frame-Options'] = 'DENY'

    # Prevent MIME type sniffing
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # XSS protection
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # Referrer policy
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # Content Security Policy
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data:; "
        "font-src 'self'; "
        "connect-src 'self'; "
        "frame-ancestors 'none';"
    )

    # HSTS (if using HTTPS)
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

    # Permissions policy
    response.headers['Permissions-Policy'] = (
        "geolocation=(), "
        "microphone=(), "
        "camera=(), "
        "payment=(), "
        "usb=(), "
        "magnetometer=(), "
        "gyroscope=(), "
        "accelerometer=()"
    )

    # Cache control for sensitive pages
    if hasattr(request, 'endpoint') and request.endpoint in ['login', 'register', 'dashboard']:
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

    return response

def check_rate_limit(ip: str, endpoint: str, limit: int = 10, window: int = 60) -> bool:
    """
    Check if IP is within rate limits
    Returns True if allowed, False if rate limited
    """
    current_time = time.time()
    key = f"{ip}:{endpoint}"
    
    # Clean old entries
    rate_limit_storage[key] = [
        timestamp for timestamp in rate_limit_storage[key]
        if current_time - timestamp < window
    ]
    
    # Check if limit exceeded
    if len(rate_limit_storage[key]) >= limit:
        return False
    
    # Add current request
    rate_limit_storage[key].append(current_time)
    return True

def is_suspicious_request(request_data: Dict[str, Any]) -> bool:
    """Check if request contains suspicious patterns"""
    suspicious_patterns = [
        # SQL injection patterns
        'union select', 'drop table', 'delete from', 'insert into',
        'update set', 'create table', 'alter table', 'exec(',
        'xp_cmdshell', 'sp_executesql',
        
        # XSS patterns
        '<script', 'javascript:', 'onload=', 'onerror=', 'onclick=',
        'eval(', 'alert(', 'document.cookie', 'window.location',
        
        # Path traversal
        '../', '..\\', '/etc/passwd', '/etc/shadow', 'web.config',
        'boot.ini', 'win.ini',
        
        # Command injection
        ';cat ', ';ls ', ';dir ', '|cat ', '|ls ', '|dir ',
        '&&', '||', '`', '$(',
        
        # LDAP injection
        '(cn=', '(uid=', '(objectclass=',
        
        # XXE patterns
        '<!entity', '<!doctype', 'system "',
    ]
    
    # Convert all values to lowercase strings for checking
    request_str = str(request_data).lower()
    
    for pattern in suspicious_patterns:
        if pattern in request_str:
            return True
    
    return False

def generate_csrf_token() -> str:
    """Generate CSRF token"""
    return hashlib.sha256(f"{time.time()}{request.remote_addr}".encode()).hexdigest()

def validate_csrf_token(token: str) -> bool:
    """Validate CSRF token (simplified for demo)"""
    # In production, this would check against stored tokens
    return len(token) == 64 and all(c in '0123456789abcdef' for c in token)

class SecurityMiddleware:
    """Security middleware for Flask"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """Security checks before each request"""
        ip = request.remote_addr
        endpoint = request.endpoint or 'unknown'
        
        # Check if IP is blocked
        if ip in blocked_ips:
            return "Access denied", 403
        
        # Rate limiting
        if not check_rate_limit(ip, endpoint):
            blocked_ips.add(ip)  # Block IP after rate limit exceeded
            return "Rate limit exceeded", 429
        
        # Check for suspicious requests
        request_data = {}
        if request.is_json:
            request_data = request.get_json() or {}
        elif request.form:
            request_data = request.form.to_dict()
        elif request.args:
            request_data = request.args.to_dict()
        
        if is_suspicious_request(request_data):
            blocked_ips.add(ip)
            return "Suspicious request detected", 403
    
    def after_request(self, response):
        """Security headers after each request"""
        setup_security_headers()
        return response

if __name__ == "__main__":
    # Test security functions
    print("🔒 Testing API Security...")
    
    # Test rate limiting
    ip = "127.0.0.1"
    for i in range(15):
        allowed = check_rate_limit(ip, "test", limit=10)
        print(f"Request {i+1}: {'Allowed' if allowed else 'Blocked'}")
    
    # Test suspicious request detection
    test_requests = [
        {"username": "admin", "password": "password"},
        {"username": "admin'; DROP TABLE users; --", "password": "test"},
        {"search": "<script>alert('xss')</script>"},
        {"file": "../../../etc/passwd"}
    ]
    
    for req in test_requests:
        suspicious = is_suspicious_request(req)
        print(f"Request {req}: {'SUSPICIOUS' if suspicious else 'Clean'}")
    
    print("✅ API Security test completed")
