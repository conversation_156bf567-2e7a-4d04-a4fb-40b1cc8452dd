#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fortress Database Module
Encrypted SQLite database with military-grade security
"""

import os
import sqlite3
import hashlib
import secrets
import time
from typing import Optional, Dict, Any
from werkzeug.security import generate_password_hash, check_password_hash
from core.crypto.hybrid_encryption import encrypt_data, decrypt_data, secure_hash, verify_hash

# Database file path
DB_PATH = "data/fortress.db"
DB_KEY = None

def get_db_key() -> str:
    """Get or generate database encryption key"""
    global DB_KEY
    if DB_KEY is None:
        key_file = "data/.db_key"
        if os.path.exists(key_file):
            with open(key_file, 'r') as f:
                DB_KEY = f.read().strip()
        else:
            # Generate new key
            DB_KEY = secrets.token_hex(32)
            os.makedirs("data", exist_ok=True)
            with open(key_file, 'w') as f:
                f.write(DB_KEY)
            # Hide the key file on Windows
            if os.name == 'nt':
                os.system(f'attrib +h "data/.db_key"')
    return DB_KEY

def get_db_connection() -> sqlite3.Connection:
    """Get encrypted database connection"""
    os.makedirs("data", exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Enable dict-like access
    
    # Enable WAL mode for better performance
    conn.execute("PRAGMA journal_mode=WAL")
    conn.execute("PRAGMA synchronous=NORMAL")
    conn.execute("PRAGMA cache_size=10000")
    conn.execute("PRAGMA temp_store=MEMORY")
    
    return conn

def init_database():
    """Initialize the database with required tables"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                hardware_id TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                login_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                encrypted_data TEXT
            )
        ''')
        
        # Sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Security logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                user_id INTEGER,
                ip_address TEXT,
                user_agent TEXT,
                details TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                severity TEXT DEFAULT 'INFO'
            )
        ''')
        
        # Honeypot table (trap for attackers)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_passwords (
                id INTEGER PRIMARY KEY,
                username TEXT,
                password TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert honeypot data
        cursor.execute('''
            INSERT OR IGNORE INTO admin_passwords (id, username, password) 
            VALUES (1, 'admin', 'admin123')
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_hardware ON users(hardware_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user ON sessions(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON security_logs(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_logs_event ON security_logs(event_type)')
        
        conn.commit()
        print("✅ Database initialized successfully")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Database initialization failed: {e}")
        raise
    finally:
        conn.close()

def create_user(username: str, email: str, password: str, hardware_id: str) -> Optional[int]:
    """Create a new user with encrypted data"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Check if user already exists
        cursor.execute('SELECT id FROM users WHERE username = ? OR email = ?', (username, email))
        if cursor.fetchone():
            return None
        
        # Hash password with salt
        password_hash = generate_password_hash(password, method='pbkdf2:sha256:100000')
        
        # Encrypt sensitive user data
        user_data = {
            'username': username,
            'email': email,
            'registration_ip': '127.0.0.1',  # Would be real IP in production
            'hardware_fingerprint': hardware_id
        }
        encrypted_data = encrypt_data(str(user_data), get_db_key())
        
        # Insert user
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, hardware_id, encrypted_data)
            VALUES (?, ?, ?, ?, ?)
        ''', (username, email, password_hash, hardware_id, encrypted_data))
        
        user_id = cursor.lastrowid
        
        # Log user creation
        log_security_event('user_created', user_id, '127.0.0.1', '', {
            'username': username,
            'email': email
        })
        
        conn.commit()
        return user_id
        
    except Exception as e:
        conn.rollback()
        print(f"❌ User creation failed: {e}")
        return None
    finally:
        conn.close()

def authenticate_user(username: str, password: str, hardware_id: str) -> Optional[Dict[str, Any]]:
    """Authenticate user with hardware binding"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get user data
        cursor.execute('''
            SELECT id, username, email, password_hash, hardware_id, 
                   login_attempts, locked_until, is_active
            FROM users 
            WHERE username = ? OR email = ?
        ''', (username, username))
        
        user = cursor.fetchone()
        if not user:
            log_security_event('login_failed', None, '127.0.0.1', '', {
                'reason': 'user_not_found',
                'username': username
            })
            return None
        
        # Check if account is locked
        if user['locked_until'] and time.time() < user['locked_until']:
            log_security_event('login_blocked', user['id'], '127.0.0.1', '', {
                'reason': 'account_locked',
                'username': username
            })
            return None
        
        # Check if account is active
        if not user['is_active']:
            log_security_event('login_blocked', user['id'], '127.0.0.1', '', {
                'reason': 'account_disabled',
                'username': username
            })
            return None
        
        # Verify password
        if not check_password_hash(user['password_hash'], password):
            # Increment login attempts
            attempts = user['login_attempts'] + 1
            locked_until = None
            
            if attempts >= 5:
                # Lock account for 30 minutes
                locked_until = time.time() + (30 * 60)
            
            cursor.execute('''
                UPDATE users 
                SET login_attempts = ?, locked_until = ?
                WHERE id = ?
            ''', (attempts, locked_until, user['id']))
            
            log_security_event('login_failed', user['id'], '127.0.0.1', '', {
                'reason': 'invalid_password',
                'username': username,
                'attempts': attempts
            })
            
            conn.commit()
            return None
        
        # Verify hardware ID
        if user['hardware_id'] != hardware_id:
            log_security_event('login_failed', user['id'], '127.0.0.1', '', {
                'reason': 'hardware_mismatch',
                'username': username,
                'expected_hw': user['hardware_id'][:8] + '...',
                'actual_hw': hardware_id[:8] + '...'
            })
            return None
        
        # Reset login attempts and update last login
        cursor.execute('''
            UPDATE users 
            SET login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (user['id'],))
        
        # Log successful login
        log_security_event('login_success', user['id'], '127.0.0.1', '', {
            'username': username
        })
        
        conn.commit()
        
        return {
            'id': user['id'],
            'username': user['username'],
            'email': user['email']
        }
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Authentication failed: {e}")
        return None
    finally:
        conn.close()

def log_security_event(event_type: str, user_id: Optional[int], ip_address: str, 
                      user_agent: str, details: Dict[str, Any], severity: str = 'INFO'):
    """Log security events"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Encrypt sensitive details
        details_str = str(details) if details else ''
        if details_str:
            details_str = encrypt_data(details_str, get_db_key())
        
        cursor.execute('''
            INSERT INTO security_logs (event_type, user_id, ip_address, user_agent, details, severity)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (event_type, user_id, ip_address, user_agent, details_str, severity))
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ Failed to log security event: {e}")
    finally:
        conn.close()

def cleanup_old_sessions():
    """Clean up expired sessions"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM sessions WHERE expires_at < CURRENT_TIMESTAMP')
        deleted = cursor.rowcount
        conn.commit()
        
        if deleted > 0:
            print(f"🧹 Cleaned up {deleted} expired sessions")
            
    except Exception as e:
        print(f"❌ Session cleanup failed: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    # Test database functionality
    print("🔒 Testing Fortress Database...")
    
    init_database()
    
    # Test user creation
    user_id = create_user("testuser", "<EMAIL>", "password123", "test_hardware_id")
    print(f"Created user ID: {user_id}")
    
    # Test authentication
    user = authenticate_user("testuser", "password123", "test_hardware_id")
    print(f"Authenticated user: {user}")
    
    print("✅ Database test completed")
