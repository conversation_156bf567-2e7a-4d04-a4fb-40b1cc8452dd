#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Supabase Database Integration
Modern cloud database with real-time capabilities
"""

import os
import json
import time
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from supabase import create_client, Client
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError

# Supabase configuration
SUPABASE_URL = "https://uceidograrzhuwobxdqc.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVjZWlkb2dyYXJ6aHV3b2J4ZHFjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MzAwNjMsImV4cCI6MjA2OTMwNjA2M30.v_5o2IKZSvwGGCBfcGdCLVTrQVZD4dvJcFbwDvx4Oac"

# Initialize Supabase client with error handling
supabase: Optional[Client] = None

try:
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("🔗 Supabase client initialized")
except Exception as e:
    print(f"❌ Failed to initialize Supabase client: {e}")
    supabase = None

# Password hasher
ph = PasswordHasher()

class SupabaseDB:
    """Modern Supabase database manager with fallback"""

    def __init__(self):
        self.client = None
        self.use_fallback = False
        self.fallback_db = None

        # Try to connect to Supabase first
        if supabase is not None:
            try:
                self.client = supabase
                # Test connection with a simple query
                test_result = self.client.table('users').select('id').limit(1).execute()
                print("✅ Supabase connection successful!")
                self.use_fallback = False
                self.init_tables()
                return
            except Exception as e:
                print(f"⚠️  Supabase connection test failed: {e}")
                print("💡 Hint: You might need to disable RLS in Supabase Dashboard")

        # Fall back to SQLite if Supabase fails
        print("🔄 Falling back to local SQLite database...")
        self.use_fallback = True
        self._init_fallback_db()
    
    def _init_fallback_db(self):
        """Initialize fallback SQLite database"""
        import sqlite3
        import os

        os.makedirs("data", exist_ok=True)
        self.fallback_db = sqlite3.connect("data/fortress.db", check_same_thread=False)
        cursor = self.fallback_db.cursor()

        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                role TEXT DEFAULT 'user'
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_type TEXT NOT NULL,
                user_id INTEGER,
                ip_address TEXT,
                user_agent TEXT,
                details TEXT,
                severity TEXT DEFAULT 'INFO',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        self.fallback_db.commit()
        print("✅ Fallback SQLite database initialized")

    def init_tables(self):
        """Initialize database tables if they don't exist"""
        if self.use_fallback:
            return

        try:
            # Check if tables exist by trying to select from them
            self.client.table('users').select('id').limit(1).execute()
            print("✅ Supabase tables already exist")
        except Exception as e:
            print("🔧 Creating Supabase tables...")
            self._create_tables()
    
    def _create_tables(self):
        """Create necessary tables in Supabase"""
        # Note: In Supabase, tables are usually created via the dashboard
        # But we can create them programmatically if needed
        
        # Users table
        try:
            # This would typically be done via Supabase dashboard or SQL
            print("📝 Please create the following tables in your Supabase dashboard:")
            print("""
            -- Users table
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT NOW(),
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                role VARCHAR(20) DEFAULT 'user'
            );
            
            -- Security events table
            CREATE TABLE security_events (
                id SERIAL PRIMARY KEY,
                event_type VARCHAR(50) NOT NULL,
                user_id INTEGER REFERENCES users(id),
                ip_address INET,
                user_agent TEXT,
                details JSONB,
                severity VARCHAR(20) DEFAULT 'INFO',
                created_at TIMESTAMP DEFAULT NOW()
            );
            
            -- Threat intelligence table
            CREATE TABLE threat_intelligence (
                id SERIAL PRIMARY KEY,
                ip_address INET NOT NULL,
                threat_type VARCHAR(50),
                threat_score INTEGER DEFAULT 0,
                country VARCHAR(2),
                is_malicious BOOLEAN DEFAULT FALSE,
                first_seen TIMESTAMP DEFAULT NOW(),
                last_seen TIMESTAMP DEFAULT NOW(),
                metadata JSONB
            );
            
            -- User sessions table
            CREATE TABLE user_sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                session_token VARCHAR(255) UNIQUE NOT NULL,
                ip_address INET,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT TRUE
            );
            """)
        except Exception as e:
            print(f"❌ Error creating tables: {e}")
    
    def create_user(self, username: str, email: str, password: str) -> Tuple[bool, str]:
        """Create a new user with modern security"""
        try:
            # Hash password with Argon2
            print(f"🔍 Creating user: {username}")
            print(f"🔍 Password length: {len(password)}")
            password_hash = ph.hash(password)
            print(f"🔍 Generated hash: {password_hash[:50]}...")

            if self.use_fallback:
                # Use SQLite fallback
                cursor = self.fallback_db.cursor()
                cursor.execute('''
                    INSERT INTO users (username, email, password_hash, is_active, role)
                    VALUES (?, ?, ?, 1, 'user')
                ''', (username, email, password_hash))

                self.fallback_db.commit()
                user_id = cursor.lastrowid

                print(f"✅ User created successfully: {username} (ID: {user_id})")
                return True, f"User {username} created successfully"
            else:
                # Use Supabase
                result = self.client.table('users').insert({
                    'username': username,
                    'email': email,
                    'password_hash': password_hash,
                    'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_active': True,
                    'role': 'user'
                }).execute()

                if result.data:
                    user_id = result.data[0]['id']
                    print(f"✅ User created successfully: {username} (ID: {user_id})")

                    # Log user creation event
                    self.log_security_event(
                        event_type="user_created",
                        user_id=user_id,
                        ip_address="127.0.0.1",
                        user_agent="System",
                        details={"username": username, "email": email},
                        severity="INFO"
                    )

                    return True, f"User {username} created successfully"
                else:
                    return False, "Failed to create user"

        except Exception as e:
            error_msg = str(e)
            if "duplicate" in error_msg.lower() or "unique" in error_msg.lower():
                return False, "Username or email already exists"
            return False, f"Database error: {error_msg}"
    
    def authenticate_user(self, username: str, password: str, ip_address: str = "127.0.0.1",
                         user_agent: str = "") -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Authenticate user with enhanced security"""
        try:
            if self.use_fallback:
                # Use SQLite fallback
                cursor = self.fallback_db.cursor()
                cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
                user_row = cursor.fetchone()

                if not user_row:
                    return False, None

                # Convert to dict
                user = {
                    'id': user_row[0],
                    'username': user_row[1],
                    'email': user_row[2],
                    'password_hash': user_row[3],
                    'created_at': user_row[4],
                    'last_login': user_row[5],
                    'is_active': user_row[6],
                    'role': user_row[7]
                }
            else:
                # Use Supabase
                result = self.client.table('users').select('*').eq('username', username).execute()

                if not result.data:
                    # Log failed login attempt
                    self.log_security_event(
                        event_type="login_failed",
                        user_id=None,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        details={"username": username, "reason": "user_not_found"},
                        severity="WARNING"
                    )
                    return False, None

                user = result.data[0]
            
            # Check if user is active
            if not user.get('is_active', True):
                self.log_security_event(
                    event_type="login_failed",
                    user_id=user['id'],
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={"username": username, "reason": "account_disabled"},
                    severity="WARNING"
                )
                return False, None
            
            # Verify password
            try:
                print(f"🔍 Verifying password for user: {username}")
                print(f"🔍 Stored hash: {user['password_hash'][:50]}...")
                ph.verify(user['password_hash'], password)
                print(f"✅ Password verification successful for: {username}")
            except VerifyMismatchError as e:
                print(f"❌ Password verification failed for: {username} - {e}")
                # Log failed login attempt
                self.log_security_event(
                    event_type="login_failed",
                    user_id=user['id'],
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={"username": username, "reason": "invalid_password"},
                    severity="WARNING"
                )
                return False, None
            except Exception as e:
                print(f"❌ Password verification error for: {username} - {e}")
                return False, None
            
            # Update last login
            if self.use_fallback:
                cursor = self.fallback_db.cursor()
                cursor.execute('UPDATE users SET last_login = ? WHERE id = ?',
                             (time.strftime('%Y-%m-%d %H:%M:%S'), user['id']))
                self.fallback_db.commit()
            else:
                self.client.table('users').update({
                    'last_login': time.strftime('%Y-%m-%d %H:%M:%S')
                }).eq('id', user['id']).execute()

            # Log successful login
            self.log_security_event(
                event_type="login_success",
                user_id=user['id'],
                ip_address=ip_address,
                user_agent=user_agent,
                details={"username": username},
                severity="INFO"
            )
            
            # Return user data (without password hash)
            user_data = {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'role': user.get('role', 'user'),
                'created_at': user.get('created_at'),
                'last_login': user.get('last_login')
            }
            
            return True, user_data
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False, None
    
    def log_security_event(self, event_type: str, user_id: Optional[int],
                          ip_address: str, user_agent: str, details: Dict[str, Any],
                          severity: str = "INFO"):
        """Log security events"""
        try:
            if self.use_fallback:
                cursor = self.fallback_db.cursor()
                cursor.execute('''
                    INSERT INTO security_events
                    (event_type, user_id, ip_address, user_agent, details, severity)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (event_type, user_id, ip_address, user_agent, json.dumps(details), severity))
                self.fallback_db.commit()
            else:
                self.client.table('security_events').insert({
                    'event_type': event_type,
                    'user_id': user_id,
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'details': details,
                    'severity': severity,
                    'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
                }).execute()

        except Exception as e:
            print(f"❌ Failed to log security event: {e}")
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            result = self.client.table('users').select('*').eq('id', user_id).execute()
            
            if result.data:
                user = result.data[0]
                # Remove password hash from response
                user.pop('password_hash', None)
                return user
            
            return None
            
        except Exception as e:
            print(f"❌ Error getting user: {e}")
            return None
    
    def get_security_events(self, limit: int = 100, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get recent security events"""
        try:
            query = self.client.table('security_events').select('*')
            
            if user_id:
                query = query.eq('user_id', user_id)
            
            result = query.order('created_at', desc=True).limit(limit).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            print(f"❌ Error getting security events: {e}")
            return []
    
    def update_threat_intelligence(self, ip_address: str, threat_type: str, 
                                 threat_score: int, country: str = None, 
                                 is_malicious: bool = False, metadata: Dict[str, Any] = None):
        """Update threat intelligence data"""
        try:
            # Check if IP already exists
            result = self.client.table('threat_intelligence').select('*').eq('ip_address', ip_address).execute()
            
            data = {
                'ip_address': ip_address,
                'threat_type': threat_type,
                'threat_score': threat_score,
                'country': country,
                'is_malicious': is_malicious,
                'last_seen': time.strftime('%Y-%m-%d %H:%M:%S'),
                'metadata': metadata or {}
            }
            
            if result.data:
                # Update existing record
                self.client.table('threat_intelligence').update(data).eq('ip_address', ip_address).execute()
            else:
                # Insert new record
                data['first_seen'] = time.strftime('%Y-%m-%d %H:%M:%S')
                self.client.table('threat_intelligence').insert(data).execute()
                
        except Exception as e:
            print(f"❌ Error updating threat intelligence: {e}")
    
    def get_threat_statistics(self) -> Dict[str, Any]:
        """Get comprehensive threat statistics"""
        try:
            # Get recent security events (last 24 hours)
            recent_events = self.client.table('security_events').select('*').gte(
                'created_at', 
                time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(time.time() - 86400))
            ).execute()
            
            # Get threat intelligence data
            threat_intel = self.client.table('threat_intelligence').select('*').execute()
            
            # Get user statistics
            users = self.client.table('users').select('id, created_at, last_login').execute()
            
            return {
                'recent_events_count': len(recent_events.data) if recent_events.data else 0,
                'total_threats': len(threat_intel.data) if threat_intel.data else 0,
                'total_users': len(users.data) if users.data else 0,
                'malicious_ips': len([t for t in (threat_intel.data or []) if t.get('is_malicious')]),
                'recent_events': recent_events.data[:10] if recent_events.data else []
            }
            
        except Exception as e:
            print(f"❌ Error getting threat statistics: {e}")
            return {}

# Global instance
db = SupabaseDB()

# Convenience functions for backward compatibility
def init_database():
    """Initialize database"""
    return db.init_tables()

def create_user(username: str, email: str, password: str) -> Tuple[bool, str]:
    """Create user"""
    return db.create_user(username, email, password)

def authenticate_user(username: str, password: str, ip_address: str = "127.0.0.1", 
                     user_agent: str = "") -> Tuple[bool, Optional[Dict[str, Any]]]:
    """Authenticate user"""
    return db.authenticate_user(username, password, ip_address, user_agent)

def log_security_event(event_type: str, user_id: Optional[int], ip_address: str, 
                      user_agent: str, details: Dict[str, Any], severity: str = "INFO"):
    """Log security event"""
    return db.log_security_event(event_type, user_id, ip_address, user_agent, details, severity)

if __name__ == "__main__":
    # Test Supabase connection
    print("🧪 Testing Supabase connection...")
    
    try:
        # Test connection
        result = db.client.table('users').select('count').execute()
        print("✅ Supabase connection successful!")
        
        # Get statistics
        stats = db.get_threat_statistics()
        print(f"📊 Database statistics: {stats}")
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        print("💡 Make sure to create the tables in your Supabase dashboard first!")
