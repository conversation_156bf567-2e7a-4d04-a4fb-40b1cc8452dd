#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hybrid Encryption Module
AES-256-GCM + RSA-2048 hybrid encryption system
"""

import os
import base64
import hashlib
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC
from cryptography.hazmat.backends import default_backend
from typing import Tuple, Optional
import secrets

class HybridCrypto:
    """Hybrid encryption using AES-256-GCM + RSA-2048"""
    
    def __init__(self):
        self.backend = default_backend()
        self._private_key = None
        self._public_key = None
        self._generate_rsa_keys()
    
    def _generate_rsa_keys(self):
        """Generate RSA key pair"""
        self._private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=self.backend
        )
        self._public_key = self._private_key.public_key()
    
    def derive_key_from_password(self, password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """Derive AES key from password using PBKDF2"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 256 bits
            salt=salt,
            iterations=100000,  # High iteration count for security
            backend=self.backend
        )
        key = kdf.derive(password.encode('utf-8'))
        return key, salt
    
    def encrypt_aes_gcm(self, data: bytes, key: bytes) -> Tuple[bytes, bytes, bytes]:
        """Encrypt data using AES-256-GCM"""
        # Generate random IV
        iv = os.urandom(12)  # 96-bit IV for GCM
        
        # Create cipher
        cipher = Cipher(
            algorithms.AES(key),
            modes.GCM(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        
        # Encrypt data
        ciphertext = encryptor.update(data) + encryptor.finalize()
        
        return ciphertext, iv, encryptor.tag
    
    def decrypt_aes_gcm(self, ciphertext: bytes, key: bytes, iv: bytes, tag: bytes) -> bytes:
        """Decrypt data using AES-256-GCM"""
        cipher = Cipher(
            algorithms.AES(key),
            modes.GCM(iv, tag),
            backend=self.backend
        )
        decryptor = cipher.decryptor()
        
        return decryptor.update(ciphertext) + decryptor.finalize()
    
    def encrypt_rsa(self, data: bytes) -> bytes:
        """Encrypt data using RSA public key"""
        return self._public_key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def decrypt_rsa(self, ciphertext: bytes) -> bytes:
        """Decrypt data using RSA private key"""
        return self._private_key.decrypt(
            ciphertext,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def hybrid_encrypt(self, data: str, password: str = None) -> str:
        """
        Hybrid encryption: AES for data, RSA for AES key
        Returns base64 encoded encrypted package
        """
        try:
            data_bytes = data.encode('utf-8')
            
            # Generate AES key
            if password:
                aes_key, salt = self.derive_key_from_password(password)
            else:
                aes_key = os.urandom(32)  # 256-bit key
                salt = b''
            
            # Encrypt data with AES
            ciphertext, iv, tag = self.encrypt_aes_gcm(data_bytes, aes_key)
            
            # Encrypt AES key with RSA
            encrypted_key = self.encrypt_rsa(aes_key)
            
            # Package everything together
            package = {
                'encrypted_key': base64.b64encode(encrypted_key).decode('utf-8'),
                'ciphertext': base64.b64encode(ciphertext).decode('utf-8'),
                'iv': base64.b64encode(iv).decode('utf-8'),
                'tag': base64.b64encode(tag).decode('utf-8'),
                'salt': base64.b64encode(salt).decode('utf-8') if salt else ''
            }
            
            # Serialize package
            package_str = '|'.join([
                package['encrypted_key'],
                package['ciphertext'],
                package['iv'],
                package['tag'],
                package['salt']
            ])
            
            return base64.b64encode(package_str.encode('utf-8')).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Encryption failed: {e}")
    
    def hybrid_decrypt(self, encrypted_package: str, password: str = None) -> str:
        """
        Hybrid decryption: RSA for AES key, AES for data
        """
        try:
            # Decode package
            package_str = base64.b64decode(encrypted_package).decode('utf-8')
            parts = package_str.split('|')
            
            if len(parts) != 5:
                raise ValueError("Invalid encrypted package format")
            
            encrypted_key = base64.b64decode(parts[0])
            ciphertext = base64.b64decode(parts[1])
            iv = base64.b64decode(parts[2])
            tag = base64.b64decode(parts[3])
            salt = base64.b64decode(parts[4]) if parts[4] else b''
            
            # Decrypt AES key with RSA
            aes_key = self.decrypt_rsa(encrypted_key)
            
            # If password was used, verify it
            if password and salt:
                derived_key, _ = self.derive_key_from_password(password, salt)
                if derived_key != aes_key:
                    raise ValueError("Invalid password")
            
            # Decrypt data with AES
            data_bytes = self.decrypt_aes_gcm(ciphertext, aes_key, iv, tag)
            
            return data_bytes.decode('utf-8')
            
        except Exception as e:
            raise Exception(f"Decryption failed: {e}")

# Global crypto instance
_crypto_instance = None

def get_crypto_instance() -> HybridCrypto:
    """Get singleton crypto instance"""
    global _crypto_instance
    if _crypto_instance is None:
        _crypto_instance = HybridCrypto()
    return _crypto_instance

def encrypt_data(data: str, password: str = None) -> str:
    """Convenience function for encryption"""
    crypto = get_crypto_instance()
    return crypto.hybrid_encrypt(data, password)

def decrypt_data(encrypted_data: str, password: str = None) -> str:
    """Convenience function for decryption"""
    crypto = get_crypto_instance()
    return crypto.hybrid_decrypt(encrypted_data, password)

def secure_hash(data: str, salt: str = None) -> str:
    """Create secure hash of data"""
    if salt is None:
        salt = secrets.token_hex(16)
    
    hash_input = f"{data}{salt}".encode('utf-8')
    hash_obj = hashlib.sha256(hash_input)
    return f"{hash_obj.hexdigest()}:{salt}"

def verify_hash(data: str, hash_with_salt: str) -> bool:
    """Verify data against hash"""
    try:
        hash_part, salt = hash_with_salt.split(':', 1)
        new_hash = secure_hash(data, salt)
        return new_hash == hash_with_salt
    except:
        return False

if __name__ == "__main__":
    # Test the hybrid encryption
    test_data = "This is a secret message for InfernoSpaceX!"
    password = "test_password_123"
    
    print("🔒 Testing Hybrid Encryption...")
    
    # Test encryption/decryption
    encrypted = encrypt_data(test_data, password)
    print(f"Encrypted: {encrypted[:50]}...")
    
    decrypted = decrypt_data(encrypted, password)
    print(f"Decrypted: {decrypted}")
    
    print(f"Success: {test_data == decrypted}")
    
    # Test hashing
    test_hash = secure_hash("password123")
    print(f"Hash: {test_hash}")
    print(f"Verify: {verify_hash('password123', test_hash)}")
