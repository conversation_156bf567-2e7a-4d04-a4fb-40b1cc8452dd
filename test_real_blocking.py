#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real Blocking Test - Demonstrate actual IP blocking
"""

import requests
import time
import json

BASE_URL = "http://127.0.0.1:5000"

def test_real_blocking():
    """Test real IP blocking functionality"""
    print("🚨 TESTING REAL IP BLOCKING (localhost whitelist disabled)")
    print("=" * 70)
    
    print("1. First, let's try a normal request:")
    try:
        response = requests.get(BASE_URL, timeout=5)
        print(f"   ✅ Normal request: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Normal request failed: {e}")
    
    print("\n2. Now let's trigger SQL injection detection:")
    try:
        # This should trigger blocking
        response = requests.post(f"{BASE_URL}/api/login", 
                               json={"username": "admin'; DROP TABLE users; --", "password": "test"},
                               timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 403:
            print("   🚨 SQL INJECTION BLOCKED!")
        else:
            print("   ⚠️  SQL injection not blocked")
    except Exception as e:
        print(f"   🚨 CONNECTION BLOCKED: {e}")
    
    print("\n3. Testing if IP is now blocked for normal requests:")
    time.sleep(1)  # Wait a moment
    
    for i in range(3):
        try:
            response = requests.get(BASE_URL, timeout=3)
            print(f"   Attempt {i+1}: Status {response.status_code}")
        except Exception as e:
            print(f"   Attempt {i+1}: 🚨 BLOCKED - {e}")
        time.sleep(1)
    
    print("\n4. Testing honeypot access:")
    honeypots = ["/admin", "/wp-admin", "/.env"]
    
    for path in honeypots:
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=3)
            print(f"   {path}: Status {response.status_code}")
        except Exception as e:
            print(f"   {path}: 🚨 BLOCKED - {e}")
    
    print("\n5. Testing XSS attack:")
    try:
        response = requests.post(f"{BASE_URL}/api/search", 
                               json={"query": "<script>alert('xss')</script>"},
                               timeout=3)
        print(f"   XSS attack: Status {response.status_code}")
    except Exception as e:
        print(f"   XSS attack: 🚨 BLOCKED - {e}")
    
    print("\n" + "=" * 70)
    print("🎯 РЕЗУЛЬТАТ:")
    print("   Если видите 'BLOCKED' или 403 статусы - защита работает!")
    print("   IP блокируется после обнаружения атаки")
    print("   Все последующие запросы с этого IP блокируются")
    print("=" * 70)

def test_rate_limiting():
    """Test rate limiting with rapid requests"""
    print("\n🚀 TESTING RATE LIMITING")
    print("=" * 50)
    
    print("Sending 120 rapid requests to test rate limiting...")
    
    blocked = 0
    allowed = 0
    
    for i in range(120):
        try:
            response = requests.get(BASE_URL, timeout=1)
            if response.status_code == 403:
                blocked += 1
            else:
                allowed += 1
        except:
            blocked += 1
        
        if i % 20 == 0:
            print(f"   Progress: {i}/120 - Allowed: {allowed}, Blocked: {blocked}")
    
    print(f"\nFinal results: {allowed} allowed, {blocked} blocked")
    if blocked > 0:
        print("✅ Rate limiting is working!")
    else:
        print("❌ Rate limiting not triggered")

if __name__ == "__main__":
    print("🛡️  InfernoSpaceX Real Blocking Test")
    print("Testing actual IP blocking functionality")
    print()
    
    # Check if server is running
    try:
        response = requests.get(BASE_URL, timeout=5)
        print("✅ Server is accessible")
    except:
        print("❌ Server not accessible. Make sure it's running.")
        exit(1)
    
    test_real_blocking()
    test_rate_limiting()
    
    print("\n💡 To re-enable localhost whitelist, uncomment the line in threat_detector.py")
    print("   This test shows how the system blocks real attackers in production!")
