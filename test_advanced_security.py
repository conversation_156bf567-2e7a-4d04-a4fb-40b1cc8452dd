#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Security System Test Suite
Comprehensive testing of all security modules
"""

import time
import json
import requests
from concurrent.futures import ThreadPoolExecutor
import threading

# Import all security modules
from core.monitoring.threat_detector import AdvancedThreatDetector
from core.monitoring.ml_anomaly_detector import AdvancedBehavioralAnalyzer
from core.monitoring.ip_intelligence import AdvancedIPAnalyzer
from core.monitoring.payload_analyzer import AdvancedPayloadAnalyzer

class SecurityTestSuite:
    """Comprehensive security testing suite"""
    
    def __init__(self):
        print("🚀 Initializing InfernoSpaceX Advanced Security Test Suite")
        print("=" * 80)
        
        # Initialize all security modules
        self.threat_detector = AdvancedThreatDetector()
        self.behavioral_analyzer = AdvancedBehavioralAnalyzer()
        self.ip_analyzer = AdvancedIPAnalyzer()
        self.payload_analyzer = AdvancedPayloadAnalyzer()
        
        print("✅ All security modules initialized")
        print()
    
    def test_threat_detection(self):
        """Test advanced threat detection capabilities"""
        print("🛡️  TESTING ADVANCED THREAT DETECTION")
        print("-" * 50)
        
        # SQL Injection Tests
        sql_payloads = [
            "admin'; DROP TABLE users; --",
            "1' UNION SELECT username, password FROM users--",
            "1; WAITFOR DELAY '00:00:05'--",
            "admin' AND 1=1--",
            "' OR '1'='1",
            "UNION/**/SELECT/**/password/**/FROM/**/users",
            "1' AND (SELECT COUNT(*) FROM users) > 0--",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        print("SQL Injection Detection:")
        sql_detected = 0
        for i, payload in enumerate(sql_payloads, 1):
            normalized = self.threat_detector._normalize_request_data({'input': payload})
            threats, score = self.threat_detector._detect_pattern_threats(normalized)
            detected = any("SQL" in threat for threat in threats)
            status = "✅ DETECTED" if detected else "❌ MISSED"
            print(f"  {i:2d}. {status} - Score: {score:2d} - {payload[:40]}...")
            if detected:
                sql_detected += 1
        
        print(f"SQL Detection Rate: {sql_detected}/{len(sql_payloads)} ({sql_detected/len(sql_payloads)*100:.1f}%)")
        print()
        
        # XSS Tests
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>",
            "';alert(String.fromCharCode(88,83,83))//';",
            "%3Cscript%3Ealert('xss')%3C/script%3E",
            "&#x3C;script&#x3E;alert('xss')&#x3C;/script&#x3E;",
            "<iframe src=javascript:alert('xss')>"
        ]
        
        print("XSS Detection:")
        xss_detected = 0
        for i, payload in enumerate(xss_payloads, 1):
            normalized = self.threat_detector._normalize_request_data({'input': payload})
            threats, score = self.threat_detector._detect_pattern_threats(normalized)
            detected = any("XSS" in threat for threat in threats)
            status = "✅ DETECTED" if detected else "❌ MISSED"
            print(f"  {i:2d}. {status} - Score: {score:2d} - {payload[:40]}...")
            if detected:
                xss_detected += 1
        
        print(f"XSS Detection Rate: {xss_detected}/{len(xss_payloads)} ({xss_detected/len(xss_payloads)*100:.1f}%)")
        print()
        
        return sql_detected, xss_detected
    
    def test_behavioral_analysis(self):
        """Test ML behavioral analysis"""
        print("🧠 TESTING ML BEHAVIORAL ANALYSIS")
        print("-" * 50)
        
        # Simulate normal user behavior
        print("Normal User Behavior:")
        normal_requests = [
            {'endpoint': '/login', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'method': 'GET', 'payload': {}, 'country': 'US'},
            {'endpoint': '/dashboard', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'method': 'GET', 'payload': {}, 'country': 'US'},
            {'endpoint': '/profile', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'method': 'GET', 'payload': {}, 'country': 'US'}
        ]
        
        normal_anomalies = 0
        for i, request in enumerate(normal_requests, 1):
            score, anomalies = self.behavioral_analyzer.analyze_behavior('***********0', request)
            print(f"  {i}. Score: {score:.2f}, Anomalies: {len(anomalies)}")
            if len(anomalies) > 0:
                normal_anomalies += 1
            time.sleep(0.1)
        
        print(f"Normal Behavior Anomalies: {normal_anomalies}/{len(normal_requests)}")
        print()
        
        # Simulate suspicious behavior
        print("Suspicious Behavior:")
        suspicious_requests = [
            {'endpoint': f'/admin{i}', 'user_agent': f'Bot{i}', 'method': 'POST', 'payload': {'cmd': f'cat /etc/passwd{i}'}, 'country': 'RU' if i % 2 else 'CN'}
            for i in range(10)
        ]
        
        suspicious_anomalies = 0
        for i, request in enumerate(suspicious_requests, 1):
            score, anomalies = self.behavioral_analyzer.analyze_behavior('*************', request)
            print(f"  {i:2d}. Score: {score:.2f}, Anomalies: {len(anomalies)} - {', '.join(anomalies[:2])}")
            if len(anomalies) > 0:
                suspicious_anomalies += 1
        
        print(f"Suspicious Behavior Detection: {suspicious_anomalies}/{len(suspicious_requests)} ({suspicious_anomalies/len(suspicious_requests)*100:.1f}%)")
        print()
        
        return suspicious_anomalies
    
    def test_ip_intelligence(self):
        """Test IP intelligence and geolocation"""
        print("🌍 TESTING IP INTELLIGENCE SYSTEM")
        print("-" * 50)
        
        test_ips = [
            ('*******', 'Google DNS'),
            ('*******', 'Cloudflare DNS'),
            ('***********', 'Private IP'),
            ('*********', 'Yandex (Russia)'),
            ('***********', 'Hosting Provider'),
            ('*************', 'Test Malicious IP')
        ]
        
        high_risk_detected = 0
        for ip, description in test_ips:
            assessment = self.ip_analyzer.get_ip_risk_assessment(ip)
            risk_level = assessment['risk_level']
            risk_score = assessment['risk_score']
            
            if risk_level in ['HIGH', 'CRITICAL']:
                high_risk_detected += 1
                status = "🚨 HIGH RISK"
            elif risk_level == 'MEDIUM':
                status = "⚠️  MEDIUM RISK"
            else:
                status = "✅ LOW RISK"
            
            print(f"  {ip:15s} - {status:12s} (Score: {risk_score:2d}) - {description}")
        
        print(f"High Risk IPs Detected: {high_risk_detected}/{len(test_ips)}")
        print()
        
        return high_risk_detected
    
    def test_payload_analysis(self):
        """Test advanced payload analysis"""
        print("🔍 TESTING ADVANCED PAYLOAD ANALYZER")
        print("-" * 50)
        
        test_payloads = [
            ("Normal data", "username=admin&password=secret123"),
            ("SQL Injection", "admin'; DROP TABLE users; --"),
            ("XSS Attack", "<script>alert('xss')</script>"),
            ("Command Injection", "; cat /etc/passwd"),
            ("Path Traversal", "../../../etc/passwd"),
            ("Encoded XSS", "%3Cscript%3Ealert('xss')%3C/script%3E"),
            ("Base64 SQL", "YWRtaW4nOyBEUk9QIFRBQkxFIHVzZXJzOyAtLQ=="),  # admin'; DROP TABLE users; --
            ("Unicode Evasion", "\\u003cscript\\u003ealert('xss')\\u003c/script\\u003e"),
            ("High Entropy", "aGVsbG8gd29ybGQgdGhpcyBpcyBhIHRlc3Q="),
            ("Null Byte", "test\x00.php")
        ]
        
        threats_detected = 0
        for description, payload in test_payloads:
            analysis = self.payload_analyzer.analyze_payload(payload)
            threat_score = analysis['threat_score']
            threats = analysis['threats']
            confidence = analysis['confidence']
            
            if threat_score > 0:
                threats_detected += 1
                status = "🚨 THREAT"
            else:
                status = "✅ CLEAN"
            
            print(f"  {description:15s} - {status:9s} (Score: {threat_score:2d}, Conf: {confidence:.2f}) - {', '.join(threats[:2])}")
        
        print(f"Threats Detected: {threats_detected}/{len(test_payloads)} ({threats_detected/len(test_payloads)*100:.1f}%)")
        print()
        
        return threats_detected
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        print("⚡ TESTING RATE LIMITING")
        print("-" * 50)
        
        test_ip = "*************"
        allowed_count = 0
        blocked_count = 0
        
        # Test IP rate limiting
        for i in range(150):
            if self.threat_detector.ip_rate_limit.is_allowed(test_ip):
                allowed_count += 1
            else:
                blocked_count += 1
        
        print(f"Rate Limiting Test (150 requests):")
        print(f"  Allowed: {allowed_count}")
        print(f"  Blocked: {blocked_count}")
        print(f"  Rate Limiting: {'✅ WORKING' if blocked_count > 0 else '❌ FAILED'}")
        print()
        
        return blocked_count > 0
    
    def test_honeypot_detection(self):
        """Test honeypot detection"""
        print("🍯 TESTING HONEYPOT DETECTION")
        print("-" * 50)
        
        class MockRequest:
            def __init__(self, path):
                self.path = path
        
        honeypot_paths = [
            "/admin", "/wp-admin", "/.env", "/phpmyadmin",
            "/backup.zip", "/config.php", "/test.php", "/.git",
            "/shell.php", "/webshell.php", "/c99.php"
        ]
        
        detected_count = 0
        for path in honeypot_paths:
            mock_request = MockRequest(path)
            if self.threat_detector.detect_honeypot_access(mock_request):
                detected_count += 1
                print(f"  ✅ DETECTED - {path}")
            else:
                print(f"  ❌ MISSED   - {path}")
        
        print(f"Honeypot Detection: {detected_count}/{len(honeypot_paths)} ({detected_count/len(honeypot_paths)*100:.1f}%)")
        print()
        
        return detected_count
    
    def performance_test(self):
        """Test system performance under load"""
        print("🚀 TESTING SYSTEM PERFORMANCE")
        print("-" * 50)
        
        def analyze_request():
            payload = "admin'; DROP TABLE users; --"
            analysis = self.payload_analyzer.analyze_payload(payload)
            return analysis['threat_score'] > 0
        
        # Performance test
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(analyze_request) for _ in range(100)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Performance Test (100 concurrent requests):")
        print(f"  Duration: {duration:.2f} seconds")
        print(f"  Requests/second: {100/duration:.1f}")
        print(f"  Average response time: {duration*1000/100:.1f}ms")
        print(f"  Detection accuracy: {sum(results)}/{len(results)} ({sum(results)/len(results)*100:.1f}%)")
        print()
        
        return duration < 5.0  # Should complete in under 5 seconds
    
    def run_comprehensive_test(self):
        """Run all security tests"""
        print("🛡️  INFERNOSPACE ADVANCED SECURITY SYSTEM")
        print("🚀 COMPREHENSIVE SECURITY TEST SUITE")
        print("=" * 80)
        print()
        
        results = {}
        
        # Run all tests
        sql_detected, xss_detected = self.test_threat_detection()
        results['sql_detection'] = sql_detected >= 7  # At least 7/8 detected
        results['xss_detection'] = xss_detected >= 7  # At least 7/8 detected
        
        suspicious_detected = self.test_behavioral_analysis()
        results['behavioral_analysis'] = suspicious_detected >= 8  # At least 8/10 detected
        
        high_risk_detected = self.test_ip_intelligence()
        results['ip_intelligence'] = high_risk_detected >= 1  # At least 1 high risk detected
        
        threats_detected = self.test_payload_analysis()
        results['payload_analysis'] = threats_detected >= 8  # At least 8/10 detected
        
        rate_limiting_works = self.test_rate_limiting()
        results['rate_limiting'] = rate_limiting_works
        
        honeypot_detected = self.test_honeypot_detection()
        results['honeypot_detection'] = honeypot_detected >= 10  # At least 10/11 detected
        
        performance_good = self.performance_test()
        results['performance'] = performance_good
        
        # Final results
        print("🎯 FINAL TEST RESULTS")
        print("=" * 80)
        
        passed_tests = 0
        total_tests = len(results)
        
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"  {test_name.replace('_', ' ').title():25s} - {status}")
            if passed:
                passed_tests += 1
        
        print()
        print(f"Overall Score: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED - MAXIMUM SECURITY ACHIEVED! 🛡️")
            print("🚀 System ready for production deployment!")
        elif passed_tests >= total_tests * 0.8:
            print("✅ EXCELLENT SECURITY LEVEL ACHIEVED!")
            print("🛡️  System provides military-grade protection!")
        else:
            print("⚠️  Some tests failed - review security configuration")
        
        print()
        print("🛡️  InfernoSpaceX Advanced Security System Test Complete")
        print("=" * 80)

if __name__ == "__main__":
    # Run comprehensive security test
    test_suite = SecurityTestSuite()
    test_suite.run_comprehensive_test()
