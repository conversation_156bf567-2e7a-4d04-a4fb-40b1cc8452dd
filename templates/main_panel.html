<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Главная панель - InfernoSpaceX</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main_panel.css') }}">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <span class="logo-icon">🚀</span>
                <span class="logo-text">InfernoSpaceX</span>
            </div>
        </div>
        
        <div class="header-right">
            <div class="user-info">
                <span class="username">{{ username }}</span>
                <div class="user-avatar">{{ username[0].upper() }}</div>
            </div>
            <button class="logout-btn" id="logout-btn" title="Выйти">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                    <polyline points="16,17 21,12 16,7"/>
                    <line x1="21" y1="12" x2="9" y2="12"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="profile">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>
                Профиль
            </button>
            <button class="tab-btn" data-tab="osint">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="M21 21l-4.35-4.35"/>
                </svg>
                Пробив
            </button>
            <button class="tab-btn" data-tab="chat">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                </svg>
                Чат
                <span class="chat-indicator" id="chat-indicator"></span>
            </button>
        </nav>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Profile Tab -->
            <div class="tab-panel active" id="profile-panel">
                <div class="panel-header">
                    <h2>Профиль пользователя</h2>
                    <p>Информация о вашем аккаунте и статистика</p>
                </div>
                
                <div class="profile-grid">
                    <div class="profile-card">
                        <div class="card-header">
                            <h3>Основная информация</h3>
                        </div>
                        <div class="card-content">
                            <div class="info-item">
                                <span class="label">Имя пользователя:</span>
                                <span class="value" id="profile-username">{{ username }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">ID пользователя:</span>
                                <span class="value" id="profile-id">{{ user_id }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Статус:</span>
                                <span class="value status online">Онлайн</span>
                            </div>
                            <div class="info-item">
                                <span class="label">Последний вход:</span>
                                <span class="value" id="last-login">Сейчас</span>
                            </div>
                        </div>
                    </div>

                    <div class="profile-card">
                        <div class="card-header">
                            <h3>Статистика</h3>
                        </div>
                        <div class="card-content">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="total-searches">0</div>
                                    <div class="stat-label">Поисков</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="total-messages">0</div>
                                    <div class="stat-label">Сообщений</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="session-time">0</div>
                                    <div class="stat-label">Минут онлайн</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- OSINT Tab -->
            <div class="tab-panel" id="osint-panel">
                <div class="panel-header">
                    <h2>Инструменты пробива</h2>
                    <p>Анализ IP адресов, доменов и другой информации</p>
                </div>

                <div class="osint-tools">
                    <div class="tool-card">
                        <div class="card-header">
                            <h3>Анализ IP адреса</h3>
                        </div>
                        <div class="card-content">
                            <div class="input-group">
                                <input type="text" id="ip-input" placeholder="Введите IP адрес (например: *******)" class="tool-input">
                                <button class="tool-btn" id="analyze-ip-btn">Анализировать</button>
                            </div>
                            <div class="result-area" id="ip-results"></div>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="card-header">
                            <h3>Анализ домена</h3>
                        </div>
                        <div class="card-content">
                            <div class="input-group">
                                <input type="text" id="domain-input" placeholder="Введите домен (например: google.com)" class="tool-input">
                                <button class="tool-btn" id="analyze-domain-btn">Анализировать</button>
                            </div>
                            <div class="result-area" id="domain-results"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-panel" id="chat-panel">
                <div class="panel-header">
                    <h2>Общий чат</h2>
                    <p>Общение с другими пользователями системы</p>
                </div>

                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <!-- Messages will be loaded here -->
                    </div>
                    
                    <div class="chat-input-area">
                        <div class="input-group">
                            <input type="text" id="chat-input" placeholder="Введите сообщение..." class="chat-input-field" maxlength="500">
                            <button class="send-btn" id="send-btn">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="22" y1="2" x2="11" y2="13"/>
                                    <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                                </svg>
                            </button>
                        </div>
                        <div class="char-counter">
                            <span id="char-count">0</span>/500
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
        <p>Загрузка...</p>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Logout Modal -->
    <div class="modal-overlay" id="logout-modal">
        <div class="modal">
            <div class="modal-header">
                <h3>Подтверждение выхода</h3>
            </div>
            <div class="modal-content">
                <p>Вы уверены, что хотите выйти из системы?</p>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="cancel-logout">Отмена</button>
                <button class="btn btn-primary" id="confirm-logout">Выйти</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/main_panel.js') }}"></script>
</body>
</html>
