{% extends "base.html" %}

{% block title %}InfernoSpaceX{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/simple-dashboard.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="simple-dashboard">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">InfernoSpaceX</div>
            <div class="user-info" id="user-display">{{ username or 'User' }}</div>
        </div>
        
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="profile">
                <i class="fas fa-user"></i>
                ПРОФИЛЬ
            </button>
            <button class="tab-button" data-tab="chat">
                <i class="fas fa-comments"></i>
                ЧАТ
            </button>
            <button class="tab-button" data-tab="lookup">
                <i class="fas fa-search"></i>
                ПРОБИВ
            </button>
        </div>
        
        <div class="logout-section">
            <button class="logout-btn" id="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                Выход
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- ПРОФИЛЬ -->
        <div class="tab-content active" id="profile-tab">
            <h1 class="page-title">ПРОФИЛЬ</h1>
            <p class="page-subtitle">Информация о вашем аккаунте</p>
            
            <div class="card">
                <div class="card-title">Основная информация</div>
                <div class="profile-info">
                    <div>
                        <div class="info-item">
                            <span class="info-label">Имя пользователя:</span>
                            <span class="info-value" id="profile-username">{{ username or 'User' }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Роль:</span>
                            <span class="info-value" id="profile-role">User</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Статус:</span>
                            <span class="info-value">Активен</span>
                        </div>
                    </div>
                    <div>
                        <div class="info-item">
                            <span class="info-label">Дата регистрации:</span>
                            <span class="info-value" id="registration-date">Загрузка...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Последний вход:</span>
                            <span class="info-value" id="last-login">Сейчас</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Hardware ID:</span>
                            <span class="info-value" id="hardware-id">Загрузка...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">Настройки</div>
                <div class="info-item">
                    <span class="info-label">Тема:</span>
                    <select id="theme-select" style="background: #1a1a1a; color: #fff; border: 1px solid #404040; padding: 5px; border-radius: 3px;">
                        <option value="dark">Темная</option>
                        <option value="light">Светлая</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- ЧАТ -->
        <div class="tab-content" id="chat-tab">
            <h1 class="page-title">ЧАТ</h1>
            <p class="page-subtitle">Общение с пользователями</p>
            
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message">
                        <div class="message-header">Система • Сейчас</div>
                        <div class="message-text">Добро пожаловать в чат InfernoSpaceX!</div>
                    </div>
                </div>
                
                <div class="chat-input-area">
                    <input type="text" class="chat-input" id="chat-input" placeholder="Введите сообщение...">
                    <button class="send-btn" id="send-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- ПРОБИВ -->
        <div class="tab-content" id="lookup-tab">
            <h1 class="page-title">ПРОБИВ</h1>
            <p class="page-subtitle">Поиск и анализ информации</p>
            
            <div class="lookup-tools">
                <!-- IP Анализ -->
                <div class="tool-section">
                    <div class="tool-title">IP Анализ</div>
                    <div class="input-group">
                        <label class="input-label">IP адрес:</label>
                        <input type="text" class="tool-input" id="ip-input" placeholder="***********">
                    </div>
                    <button class="tool-btn" id="analyze-ip-btn">Анализировать IP</button>
                    <div class="results-area" id="ip-results">Введите IP адрес для анализа</div>
                </div>

                <!-- Поиск пользователя -->
                <div class="tool-section">
                    <div class="tool-title">Поиск пользователя</div>
                    <div class="input-group">
                        <label class="input-label">Имя пользователя:</label>
                        <input type="text" class="tool-input" id="user-input" placeholder="username">
                    </div>
                    <button class="tool-btn" id="search-user-btn">Найти пользователя</button>
                    <div class="results-area" id="user-results">Введите имя пользователя для поиска</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/simple-dashboard.js') }}"></script>
{% endblock %}
