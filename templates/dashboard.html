{% extends "base.html" %}

{% block title %}Midnight Dashboard - InfernoSpaceX{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/midnight-dashboard.css') }}">
{% endblock %}

{% block content %}
<!-- Advanced Background Effects -->
<div class="cyber-grid"></div>
<div class="particles-container" id="particles-container"></div>

<div class="dashboard-container">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-left">
            <div class="logo-icon">🚀</div>
            <div class="header-info">
                <h1 class="header-title">InfernoSpaceX</h1>
                <p class="header-subtitle">Центр управления</p>
            </div>
        </div>
        
        <div class="header-right">
            <div class="user-info">
                <span class="user-name" id="user-name">Пользователь</span>
                <div class="user-avatar">👤</div>
            </div>
            
            <button class="btn btn-ghost" id="logout-btn" title="Выйти">
                <span class="logout-icon">🚪</span>
            </button>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <div class="welcome-card neon-border">
                <div class="welcome-icon">🌟</div>
                <div class="welcome-content">
                    <h2 class="welcome-title holographic">Добро пожаловать в будущее!</h2>
                    <p class="welcome-text">Midnight Dashboard активирован</p>
                </div>
            </div>
        </section>
        
        <!-- Stats Grid -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="color: #10B981;">🚀</div>
                    <div class="stat-content">
                        <div class="stat-number">15</div>
                        <div class="stat-label">Активные миссии</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="color: #3B82F6;">✅</div>
                    <div class="stat-content">
                        <div class="stat-number">127</div>
                        <div class="stat-label">Успешных запусков</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="color: #ec4899;">🎯</div>
                    <div class="stat-content">
                        <div class="stat-number">89%</div>
                        <div class="stat-label">Точность посадки</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="color: #8b5cf6;">⚡</div>
                    <div class="stat-content">
                        <div class="stat-number">Онлайн</div>
                        <div class="stat-label">Статус системы</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Actions Section -->
        <section class="actions-section">
            <div class="actions-grid">
                <button class="action-card" id="new-mission-btn">
                    <div class="action-icon">➕</div>
                    <div class="action-content">
                        <h3 class="action-title">Новая миссия</h3>
                        <p class="action-description">Запустить новую космическую миссию</p>
                    </div>
                </button>
                
                <button class="action-card" id="analytics-btn">
                    <div class="action-icon">📊</div>
                    <div class="action-content">
                        <h3 class="action-title">Аналитика</h3>
                        <p class="action-description">Просмотр статистики и отчетов</p>
                    </div>
                </button>

                <button class="action-card" id="settings-btn">
                    <div class="action-icon">⚙️</div>
                    <div class="action-content">
                        <h3 class="action-title">Настройки</h3>
                        <p class="action-description">Конфигурация системы</p>
                    </div>
                </button>

                <a href="/security-dashboard" class="action-card" id="security-btn" style="text-decoration: none; color: inherit;">
                    <div class="action-icon">🔒</div>
                    <div class="action-content">
                        <h3 class="action-title">Безопасность</h3>
                        <p class="action-description">Продвинутый мониторинг угроз</p>
                    </div>
                </a>
            </div>
        </section>
        
        <!-- Status Indicator -->
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span class="status-text">Система активна</span>
        </div>
    </main>
</div>

<!-- Logout Confirmation Modal -->
<div id="logout-modal" class="modal hidden">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Подтверждение выхода</h2>
        </div>
        <div class="modal-body">
            <p>Вы уверены, что хотите выйти из системы?</p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" id="cancel-logout">Отмена</button>
            <button class="btn btn-primary" id="confirm-logout">Выйти</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/midnight-dashboard.js') }}"></script>
{% endblock %}
