{% extends "base.html" %}

{% block title %}InfernoSpaceX - Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/premium-dashboard.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="premium-dashboard theme-dark" id="dashboard-container">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="header-left">
            <div class="breadcrumb">
                <div class="breadcrumb-item">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </div>
                <div class="breadcrumb-separator">/</div>
                <div class="breadcrumb-item">
                    <span id="current-page">Profile</span>
                </div>
            </div>
        </div>
        
        <div class="header-right">
            <button class="theme-toggle" id="theme-toggle">
                <i class="fas fa-moon"></i>
            </button>
            
            <div class="user-menu" id="user-menu">
                <div class="user-avatar" id="header-avatar">U</div>
                <div class="user-info">
                    <div class="user-name" id="header-username">{{ username or 'User' }}</div>
                    <div class="user-role" id="header-role">User</div>
                </div>
                <i class="fas fa-chevron-down" style="font-size: 0.75rem; color: var(--text-tertiary);"></i>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="dashboard-sidebar">
        <div class="sidebar-header">
            <div class="brand-logo">
                <i class="fas fa-fire"></i>
            </div>
            <div class="brand-text">
                <div class="brand-name">InfernoSpaceX</div>
                <div class="brand-subtitle">Control Panel</div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-items">
                    <button class="nav-item active" data-tab="profile">
                        <div class="nav-icon"><i class="fas fa-user"></i></div>
                        <span>Profile</span>
                    </button>
                    <button class="nav-item" data-tab="analytics">
                        <div class="nav-icon"><i class="fas fa-chart-line"></i></div>
                        <span>Analytics</span>
                    </button>
                    <button class="nav-item" data-tab="security">
                        <div class="nav-icon"><i class="fas fa-shield-alt"></i></div>
                        <span>Security</span>
                    </button>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">Tools</div>
                <div class="nav-items">
                    <button class="nav-item" data-tab="lookup">
                        <div class="nav-icon"><i class="fas fa-search"></i></div>
                        <span>IP Lookup</span>
                    </button>
                    <button class="nav-item" data-tab="chat">
                        <div class="nav-icon"><i class="fas fa-comments"></i></div>
                        <span>Chat</span>
                    </button>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <div class="nav-items">
                    <button class="nav-item" data-tab="settings">
                        <div class="nav-icon"><i class="fas fa-cog"></i></div>
                        <span>Settings</span>
                    </button>
                    <button class="nav-item" id="logout-btn">
                        <div class="nav-icon"><i class="fas fa-sign-out-alt"></i></div>
                        <span>Logout</span>
                    </button>
                </div>
            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="dashboard-main">
        <div class="main-content">
            <!-- Profile Tab -->
            <div class="tab-content active" id="profile-tab">
                <div class="page-header">
                    <h1 class="page-title">Profile</h1>
                    <p class="page-description">Manage your account information and preferences</p>
                </div>
                
                <div class="grid grid-cols-3">
                    <!-- Profile Card -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-user-circle"></i>
                                Account Info
                            </div>
                            <div class="card-description">Your personal information</div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <div class="profile-avatar" id="profile-avatar">U</div>
                                <div class="profile-details">
                                    <h3 id="profile-name">{{ username or 'User' }}</h3>
                                    <div class="profile-role" id="profile-role-badge">User</div>
                                </div>
                            </div>
                            
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-label">Member Since</div>
                                    <div class="stat-value" id="member-since">Loading...</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Last Login</div>
                                    <div class="stat-value" id="last-login">Loading...</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Hardware ID</div>
                                    <div class="stat-value" id="hardware-id">Loading...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Card -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-activity"></i>
                                Activity
                            </div>
                            <div class="card-description">Your recent activity</div>
                        </div>
                        <div class="card-content">
                            <div class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-title">Logged in</div>
                                        <div class="activity-time">Just now</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-shield-check"></i>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-title">Security scan completed</div>
                                        <div class="activity-time">5 minutes ago</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-user-edit"></i>
                                    </div>
                                    <div class="activity-details">
                                        <div class="activity-title">Profile updated</div>
                                        <div class="activity-time">1 hour ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                Quick Stats
                            </div>
                            <div class="card-description">Overview of your account</div>
                        </div>
                        <div class="card-content">
                            <div class="quick-stats">
                                <div class="quick-stat">
                                    <div class="quick-stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="quick-stat-details">
                                        <div class="quick-stat-value" id="session-time">0m</div>
                                        <div class="quick-stat-label">Session Time</div>
                                    </div>
                                </div>
                                <div class="quick-stat">
                                    <div class="quick-stat-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="quick-stat-details">
                                        <div class="quick-stat-value">Active</div>
                                        <div class="quick-stat-label">Security Status</div>
                                    </div>
                                </div>
                                <div class="quick-stat">
                                    <div class="quick-stat-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="quick-stat-details">
                                        <div class="quick-stat-value" id="user-location">Unknown</div>
                                        <div class="quick-stat-label">Location</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div class="tab-content" id="analytics-tab">
                <div class="page-header">
                    <h1 class="page-title">Analytics</h1>
                    <p class="page-description">System performance and usage statistics</p>
                </div>
                
                <div class="grid grid-cols-2">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i>
                                Performance Metrics
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="metrics-grid">
                                <div class="metric">
                                    <div class="metric-value">99.9%</div>
                                    <div class="metric-label">Uptime</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">12ms</div>
                                    <div class="metric-label">Response Time</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">1,234</div>
                                    <div class="metric-label">Requests</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">0</div>
                                    <div class="metric-label">Errors</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-users"></i>
                                User Statistics
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="user-stats">
                                <div class="user-stat">
                                    <span class="user-stat-label">Total Users:</span>
                                    <span class="user-stat-value">1</span>
                                </div>
                                <div class="user-stat">
                                    <span class="user-stat-label">Active Sessions:</span>
                                    <span class="user-stat-value">1</span>
                                </div>
                                <div class="user-stat">
                                    <span class="user-stat-label">New Today:</span>
                                    <span class="user-stat-value">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-tab">
                <div class="page-header">
                    <h1 class="page-title">Security Center</h1>
                    <p class="page-description">Monitor and manage security threats</p>
                </div>
                
                <div class="grid grid-cols-2">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-shield-check"></i>
                                Threat Detection
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="security-status">
                                <div class="status-indicator status-safe">
                                    <i class="fas fa-check-circle"></i>
                                    <span>All Systems Secure</span>
                                </div>
                                <div class="security-metrics">
                                    <div class="security-metric">
                                        <span class="metric-label">Blocked IPs:</span>
                                        <span class="metric-value" id="blocked-ips">0</span>
                                    </div>
                                    <div class="security-metric">
                                        <span class="metric-label">Threats Detected:</span>
                                        <span class="metric-value" id="threats-detected">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-cogs"></i>
                                Quick Actions
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="action-buttons">
                                <button class="action-btn primary">
                                    <i class="fas fa-sync-alt"></i>
                                    Refresh Stats
                                </button>
                                <button class="action-btn secondary">
                                    <i class="fas fa-download"></i>
                                    Export Logs
                                </button>
                                <button class="action-btn secondary">
                                    <i class="fas fa-external-link-alt"></i>
                                    Security Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other tabs will be added here -->
            <div class="tab-content" id="lookup-tab">
                <div class="page-header">
                    <h1 class="page-title">IP Lookup</h1>
                    <p class="page-description">Analyze IP addresses and gather intelligence</p>
                </div>
                <!-- Content will be added -->
            </div>

            <div class="tab-content" id="chat-tab">
                <div class="page-header">
                    <h1 class="page-title">Chat</h1>
                    <p class="page-description">Communicate with other users</p>
                </div>
                <!-- Content will be added -->
            </div>

            <div class="tab-content" id="settings-tab">
                <div class="page-header">
                    <h1 class="page-title">Settings</h1>
                    <p class="page-description">Customize your experience</p>
                </div>
                <!-- Content will be added -->
            </div>
        </div>
    </main>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/premium-dashboard.js') }}"></script>
{% endblock %}
