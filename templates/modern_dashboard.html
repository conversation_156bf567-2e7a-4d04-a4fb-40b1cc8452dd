{% extends "base.html" %}

{% block title %}InfernoSpaceX Command Center{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern_dashboard.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="command-center fullscreen-override">
    <!-- Top Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand">
            <div class="brand-icon">🔥</div>
            <div class="brand-text">
                <h1>InfernoSpaceX</h1>
                <span>Control Panel</span>
            </div>
        </div>
        
        <div class="nav-user">
            <div class="user-avatar" id="nav-user-avatar">
                {{ (session.username or 'U')[0].upper() }}
            </div>
            <div class="user-details">
                <span class="user-name" id="username">{{ session.username or user_id }}</span>
                <span class="user-role" id="nav-user-role">Пользователь</span>
            </div>
            <button class="logout-btn" data-action="logout">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Sidebar with Tabs -->
        <aside class="sidebar">
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="profile">
                    <i class="fas fa-user-circle"></i>
                    <span>Профиль</span>
                </button>
                <button class="tab-btn" data-tab="probiv">
                    <i class="fas fa-search"></i>
                    <span>Пробив</span>
                </button>
                <button class="tab-btn" data-tab="chat">
                    <i class="fas fa-comments"></i>
                    <span>Чат</span>
                </button>
                <button class="tab-btn" data-tab="security">
                    <i class="fas fa-shield-alt"></i>
                    <span>Безопасность</span>
                </button>
            </div>
        </aside>

        <!-- Content Panels -->
        <main class="content-area">
            <!-- Profile Tab -->
            <div class="tab-content active" id="profile-tab">
                <div class="content-header">
                    <h2><i class="fas fa-user-circle"></i> Профиль пользователя</h2>
                    <p>Управление профилем и настройками</p>
                </div>
                
                <div class="profile-grid">
                    <!-- User Avatar and Basic Info -->
                    <div class="profile-card">
                        <div class="card-header">
                            <h3><i class="fas fa-user-circle"></i> Профиль пользователя</h3>
                        </div>
                        <div class="card-content">
                            <div class="profile-avatar-section">
                                <div class="profile-avatar" id="profile-avatar">
                                    {{ (session.username or 'U')[0].upper() }}
                                </div>
                                <div class="profile-basic-info">
                                    <h4 id="profile-username">{{ session.username or user_id }}</h4>
                                    <div class="role-badge-large" id="profile-role">User</div>
                                </div>
                            </div>

                            <div class="profile-stats">
                                <div class="stat-box">
                                    <i class="fas fa-calendar-alt"></i>
                                    <div>
                                        <span class="stat-label">Дата регистрации</span>
                                        <span class="stat-value" id="registration-date">Загрузка...</span>
                                    </div>
                                </div>
                                <div class="stat-box">
                                    <i class="fas fa-clock"></i>
                                    <div>
                                        <span class="stat-label">Последний вход</span>
                                        <span class="stat-value" id="last-login">Сейчас</span>
                                    </div>
                                </div>
                                <div class="stat-box">
                                    <i class="fas fa-microchip"></i>
                                    <div>
                                        <span class="stat-label">Hardware ID</span>
                                        <span class="stat-value" id="hardware-id">Загрузка...</span>
                                    </div>
                                </div>
                                <div class="stat-box">
                                    <i class="fas fa-shield-alt"></i>
                                    <div>
                                        <span class="stat-label">Статус безопасности</span>
                                        <span class="stat-value status-secure">Защищен</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="profile-card">
                        <div class="card-header">
                            <h3><i class="fas fa-cog"></i> Настройки</h3>
                        </div>
                        <div class="card-content">
                            <div class="setting-item">
                                <label>Тема интерфейса:</label>
                                <select class="setting-select" id="theme-selector">
                                    <option value="dark">Темная</option>
                                    <option value="light">Светлая</option>
                                    <option value="auto">Авто</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label>Уведомления:</label>
                                <label class="toggle">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Probiv Tab -->
            <div class="tab-content" id="probiv-tab">
                <div class="content-header">
                    <h2><i class="fas fa-search"></i> Система пробива</h2>
                    <p>Поиск и анализ информации</p>
                </div>
                
                <div class="probiv-tools">
                    <div class="tool-card">
                        <div class="card-header">
                            <h3><i class="fas fa-globe"></i> IP Анализ</h3>
                        </div>
                        <div class="card-content">
                            <div class="input-group">
                                <input type="text" id="ip-input" placeholder="Введите IP адрес" class="tool-input">
                                <button class="btn btn-primary" id="analyze-ip">Анализировать</button>
                            </div>
                            <div id="ip-results" class="results-area"></div>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="card-header">
                            <h3><i class="fas fa-user-secret"></i> Поиск пользователя</h3>
                        </div>
                        <div class="card-content">
                            <div class="input-group">
                                <input type="text" id="user-input" placeholder="Введите имя пользователя" class="tool-input">
                                <button class="btn btn-primary" id="search-user">Найти</button>
                            </div>
                            <div id="user-results" class="results-area"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-content" id="chat-tab">
                <div class="content-header">
                    <h2><i class="fas fa-comments"></i> Общий чат</h2>
                    <p>Общение с пользователями</p>
                </div>

                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="message system">
                            <div class="message-content">
                                <i class="fas fa-info-circle"></i>
                                Добро пожаловать в общий чат InfernoSpaceX!
                            </div>
                            <div class="message-time">Сейчас</div>
                        </div>
                    </div>
                    
                    <div class="chat-input-area">
                        <div class="input-group">
                            <input type="text" id="chat-input" placeholder="Введите сообщение..." class="chat-input">
                            <button class="btn btn-primary" id="send-message">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-tab">
                <div class="content-header">
                    <h2><i class="fas fa-shield-alt"></i> Центр безопасности</h2>
                    <p>Мониторинг и управление безопасностью</p>
                </div>
                
                <div class="security-grid">
                    <div class="security-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-line"></i> Статистика угроз</h3>
                        </div>
                        <div class="card-content">
                            <div class="stat-item">
                                <span class="stat-label">Заблокированные IP:</span>
                                <span class="stat-value" id="blocked-ips">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Угрозы за час:</span>
                                <span class="stat-value" id="recent-threats">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Уровень защиты:</span>
                                <span class="stat-value security-level">МАКСИМАЛЬНЫЙ</span>
                            </div>
                        </div>
                    </div>

                    <div class="security-card">
                        <div class="card-header">
                            <h3><i class="fas fa-cogs"></i> Быстрые действия</h3>
                        </div>
                        <div class="card-content">
                            <button class="btn btn-secondary full-width" onclick="window.open('/security-dashboard')">
                                <i class="fas fa-external-link-alt"></i>
                                Открыть Security Dashboard
                            </button>
                            <button class="btn btn-secondary full-width" id="refresh-stats">
                                <i class="fas fa-sync-alt"></i>
                                Обновить статистику
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/modern_dashboard.js') }}"></script>
{% endblock %}
