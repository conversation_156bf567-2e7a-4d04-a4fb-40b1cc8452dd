{% extends "base.html" %}

{% block title %}Advanced Security Dashboard - InfernoSpaceX{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<style>
.security-dashboard { padding: 20px; max-width: 1400px; margin: 0 auto; }
.security-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
.threat-chart { background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; backdrop-filter: blur(10px); margin: 20px 0; }
.threat-level-critical { color: #ff4757; font-weight: bold; }
.threat-level-high { color: #ff6b35; font-weight: bold; }
.threat-level-medium { color: #ffa502; font-weight: bold; }
.threat-level-low { color: #26de81; font-weight: bold; }
.ip-analysis { margin: 20px 0; }
.blocked-ips { max-height: 200px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; }
.real-time-stats { animation: pulse 2s infinite; }
@keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
.stat-card { background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; backdrop-filter: blur(10px); text-align: center; }
.stat-value { font-size: 2em; font-weight: bold; color: #00ff88; margin: 10px 0; }
.stat-title { color: #fff; margin-bottom: 10px; }
.stat-description { color: #ccc; font-size: 0.9em; }
.section { margin: 30px 0; }
.section-title { color: #fff; font-size: 1.5em; margin-bottom: 20px; display: flex; align-items: center; gap: 10px; }
.input-group { display: flex; gap: 10px; margin: 15px 0; }
.input-group input, .input-group select { padding: 10px; border-radius: 5px; border: 1px solid #444; background: rgba(0,0,0,0.5); color: #fff; }
.btn { padding: 10px 20px; border-radius: 5px; border: none; cursor: pointer; font-weight: bold; }
.btn-primary { background: #007bff; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn:hover { opacity: 0.8; }
.threat-types { display: flex; flex-direction: column; gap: 10px; }
.threat-type-item { display: flex; align-items: center; gap: 15px; }
.threat-name { min-width: 150px; color: #fff; }
.threat-bar { flex: 1; height: 20px; background: rgba(0,0,0,0.3); border-radius: 10px; overflow: hidden; }
.threat-fill { height: 100%; background: linear-gradient(90deg, #ff4757, #ffa502, #26de81); transition: width 0.3s; }
.threat-count { color: #00ff88; font-weight: bold; }
.geo-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
.geo-item { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; text-align: center; }
.country-name { color: #fff; font-weight: bold; display: block; }
.attack-count { color: #ff4757; font-size: 1.2em; }
.modules-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 15px; }
.module-item { background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; display: flex; align-items: center; gap: 15px; }
.module-status { font-size: 1.2em; }
.module-name { color: #fff; font-weight: bold; }
.module-description { color: #ccc; font-size: 0.9em; }
.ip-analysis-details { background: rgba(0,0,0,0.3); padding: 20px; border-radius: 8px; margin: 15px 0; }
.ip-analysis-details h5 { color: #00ff88; margin-bottom: 15px; }
.ip-analysis-details p { color: #fff; margin: 8px 0; }
</style>
{% endblock %}

{% block content %}
<div class="security-dashboard">
    <div class="dashboard-header">
        <h1 style="color: #fff; text-align: center; margin-bottom: 30px;">
            <span style="font-size: 2em;">🛡️</span>
            InfernoSpaceX Advanced Security Dashboard
        </h1>
    </div>

    <!-- Real-time Security Statistics -->
    <div class="security-stats">
        <div class="stat-card real-time-stats">
            <div style="font-size: 3em;">🛡️</div>
            <h3 class="stat-title">Заблокированные IP</h3>
            <p class="stat-value" id="blocked-ips-count">Загрузка...</p>
            <p class="stat-description">Активных блокировок</p>
        </div>

        <div class="stat-card real-time-stats">
            <div style="font-size: 3em;">⚠️</div>
            <h3 class="stat-title">Угрозы за час</h3>
            <p class="stat-value" id="recent-threats">Загрузка...</p>
            <p class="stat-description">Обнаруженных атак</p>
        </div>

        <div class="stat-card real-time-stats">
            <div style="font-size: 3em;">🌍</div>
            <h3 class="stat-title">Страны атакующих</h3>
            <p class="stat-value" id="attacking-countries">Загрузка...</p>
            <p class="stat-description">Уникальных стран</p>
        </div>

        <div class="stat-card real-time-stats">
            <div style="font-size: 3em;">🧠</div>
            <h3 class="stat-title">ML Аномалии</h3>
            <p class="stat-value" id="ml-anomalies">Загрузка...</p>
            <p class="stat-description">Поведенческих аномалий</p>
        </div>
    </div>

    <!-- Threat Analysis Charts -->
    <div class="section">
        <h2 class="section-title">
            <span>📊</span>
            Анализ угроз в реальном времени
        </h2>
        <div class="threat-chart">
            <h3 style="color: #fff; margin-bottom: 20px;">Топ типов угроз</h3>
            <div id="threat-types-chart">Загрузка данных...</div>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">
            <span>🌍</span>
            Географическое распределение атак
        </h2>
        <div class="threat-chart">
            <div id="geo-distribution">Загрузка карты угроз...</div>
        </div>
    </div>

    <!-- IP Analysis Tool -->
    <div class="section ip-analysis">
        <h2 class="section-title">
            <span>🔍</span>
            Анализ IP адресов
        </h2>
        <div class="ip-analyzer">
            <div class="input-group">
                <input type="text" id="ip-input" placeholder="Введите IP адрес для анализа" 
                       pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                <button id="analyze-ip-btn" class="btn btn-primary">Анализировать</button>
            </div>
            <div id="ip-analysis-result" style="display: none;">
                <h4 style="color: #fff;">Результат анализа:</h4>
                <div id="ip-details"></div>
            </div>
        </div>
    </div>

    <!-- Advanced Security Modules -->
    <div class="section">
        <h2 class="section-title">
            <span>🔐</span>
            Продвинутые модули защиты
        </h2>
        <div class="modules-list">
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Advanced Threat Detector</div>
                    <div class="module-description">ML-based threat detection with 15+ attack patterns</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Behavioral Analysis</div>
                    <div class="module-description">Real-time user behavior anomaly detection</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">IP Intelligence</div>
                    <div class="module-description">Geolocation, reputation, and threat intelligence</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Payload Analyzer</div>
                    <div class="module-description">Deep payload inspection with evasion detection</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Hybrid Encryption</div>
                    <div class="module-description">AES-256-GCM + RSA-2048 military-grade crypto</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Anti-Analysis Suite</div>
                    <div class="module-description">VM, debugger, and sandbox detection</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Hardware Fingerprinting</div>
                    <div class="module-description">Unique device binding with 7+ hardware identifiers</div>
                </div>
            </div>
            <div class="module-item">
                <span class="module-status">✅</span>
                <div>
                    <div class="module-name">Advanced Honeypots</div>
                    <div class="module-description">50+ trap endpoints with intelligent logging</div>
                </div>
            </div>
        </div>
    </div>

    <!-- IP Management -->
    <div class="section">
        <h2 class="section-title">
            <span>🚫</span>
            Управление IP адресами
        </h2>
        <div class="ip-management">
            <div class="input-group">
                <input type="text" id="manage-ip-input" placeholder="IP адрес">
                <select id="action-select">
                    <option value="whitelist">Добавить в белый список</option>
                    <option value="block">Заблокировать</option>
                </select>
                <button id="manage-ip-btn" class="btn btn-secondary">Выполнить</button>
            </div>
        </div>
        <div class="blocked-ips">
            <h4 style="color: #fff; margin-bottom: 15px;">Заблокированные IP:</h4>
            <div id="blocked-ips-list">Загрузка списка...</div>
        </div>
    </div>

    <!-- Back to Dashboard -->
    <div style="text-align: center; margin: 40px 0;">
        <a href="/dashboard" class="btn btn-primary">← Вернуться к основной панели</a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Advanced Security Dashboard
class SecurityDashboard {
    constructor() {
        this.updateInterval = 5000; // 5 seconds
        this.init();
    }
    
    init() {
        this.loadSecurityStats();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }
    
    async loadSecurityStats() {
        try {
            const response = await fetch('/api/security/stats');
            const data = await response.json();
            
            if (data.success) {
                this.updateStats(data.statistics);
            }
        } catch (error) {
            console.error('Failed to load security stats:', error);
        }
    }
    
    updateStats(stats) {
        document.getElementById('blocked-ips-count').textContent = stats.total_blocked_ips || 0;
        document.getElementById('recent-threats').textContent = stats.recent_threat_events || 0;
        document.getElementById('attacking-countries').textContent = 
            Object.keys(stats.geographic_distribution || {}).length;
        document.getElementById('ml-anomalies').textContent = 
            stats.average_threat_score ? Math.round(stats.average_threat_score) : 0;
        
        this.updateThreatChart(stats.threat_type_distribution || {});
        this.updateGeoChart(stats.geographic_distribution || {});
        this.updateBlockedIPsList(stats.top_attacking_ips || {});
    }
    
    updateThreatChart(threatTypes) {
        const chartDiv = document.getElementById('threat-types-chart');
        let html = '<div class="threat-types">';
        
        const maxCount = Math.max(...Object.values(threatTypes), 1);
        
        for (const [type, count] of Object.entries(threatTypes)) {
            const percentage = (count / maxCount) * 100;
            html += `
                <div class="threat-type-item">
                    <span class="threat-name">${type}</span>
                    <div class="threat-bar">
                        <div class="threat-fill" style="width: ${percentage}%"></div>
                    </div>
                    <span class="threat-count">${count}</span>
                </div>
            `;
        }
        
        if (Object.keys(threatTypes).length === 0) {
            html += '<p style="color: #26de81; text-align: center;">Угроз не обнаружено</p>';
        }
        
        html += '</div>';
        chartDiv.innerHTML = html;
    }
    
    updateGeoChart(geoData) {
        const geoDiv = document.getElementById('geo-distribution');
        let html = '<div class="geo-stats">';
        
        for (const [country, count] of Object.entries(geoData)) {
            html += `
                <div class="geo-item">
                    <span class="country-name">${country}</span>
                    <span class="attack-count">${count} атак</span>
                </div>
            `;
        }
        
        if (Object.keys(geoData).length === 0) {
            html += '<p style="color: #26de81; text-align: center;">Атак из других стран не обнаружено</p>';
        }
        
        html += '</div>';
        geoDiv.innerHTML = html;
    }
    
    updateBlockedIPsList(blockedIPs) {
        const listDiv = document.getElementById('blocked-ips-list');
        let html = '';
        
        for (const [ip, count] of Object.entries(blockedIPs)) {
            html += `<div style="color: #ff4757; margin: 5px 0;">${ip} (${count} атак)</div>`;
        }
        
        if (Object.keys(blockedIPs).length === 0) {
            html = '<div style="color: #26de81;">Заблокированных IP нет</div>';
        }
        
        listDiv.innerHTML = html;
    }
    
    setupEventListeners() {
        // IP Analysis
        document.getElementById('analyze-ip-btn').addEventListener('click', () => {
            this.analyzeIP();
        });
        
        // IP Management
        document.getElementById('manage-ip-btn').addEventListener('click', () => {
            this.manageIP();
        });
        
        // Enter key support
        document.getElementById('ip-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.analyzeIP();
        });
    }
    
    async analyzeIP() {
        const ip = document.getElementById('ip-input').value.trim();
        if (!ip) return;
        
        try {
            const response = await fetch(`/api/security/ip-analysis/${ip}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayIPAnalysis(data);
            } else {
                alert('Ошибка анализа IP: ' + data.error);
            }
        } catch (error) {
            alert('Ошибка соединения: ' + error.message);
        }
    }
    
    displayIPAnalysis(data) {
        const resultDiv = document.getElementById('ip-analysis-result');
        const detailsDiv = document.getElementById('ip-details');
        
        const risk = data.risk_assessment;
        const threat = data.threat_profile;
        
        let riskClass = 'threat-level-low';
        if (risk.risk_level === 'CRITICAL') riskClass = 'threat-level-critical';
        else if (risk.risk_level === 'HIGH') riskClass = 'threat-level-high';
        else if (risk.risk_level === 'MEDIUM') riskClass = 'threat-level-medium';
        
        detailsDiv.innerHTML = `
            <div class="ip-analysis-details">
                <h5>IP: ${data.ip}</h5>
                <p><strong>Уровень риска:</strong> <span class="${riskClass}">${risk.risk_level}</span> (${risk.risk_score})</p>
                <p><strong>Страна:</strong> ${risk.intelligence.country || 'Неизвестно'}</p>
                <p><strong>ISP:</strong> ${risk.intelligence.isp || 'Неизвестно'}</p>
                <p><strong>Факторы риска:</strong> ${risk.risk_factors.join(', ') || 'Нет'}</p>
                <p><strong>Рекомендация:</strong> ${risk.recommendation}</p>
                <p><strong>Запросов:</strong> ${threat.profile?.request_count || 0}</p>
                <p><strong>История угроз:</strong> ${threat.profile?.threat_history?.length || 0}</p>
            </div>
        `;
        
        resultDiv.style.display = 'block';
    }
    
    async manageIP() {
        const ip = document.getElementById('manage-ip-input').value.trim();
        const action = document.getElementById('action-select').value;
        
        if (!ip) return;
        
        try {
            const endpoint = action === 'whitelist' ? '/api/security/whitelist-ip' : '/api/security/block-ip';
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ip: ip, reason: 'Manual action from security dashboard' })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert(data.message);
                document.getElementById('manage-ip-input').value = '';
                this.loadSecurityStats(); // Refresh stats
            } else {
                alert('Ошибка: ' + data.error);
            }
        } catch (error) {
            alert('Ошибка соединения: ' + error.message);
        }
    }
    
    startRealTimeUpdates() {
        setInterval(() => {
            this.loadSecurityStats();
        }, this.updateInterval);
    }
}

// Initialize security dashboard
document.addEventListener('DOMContentLoaded', () => {
    new SecurityDashboard();
});
</script>
{% endblock %}
