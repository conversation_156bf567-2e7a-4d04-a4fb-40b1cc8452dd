{% extends "base.html" %}

{% block title %}Регистрация - InfernoSpaceX{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card glass-card">
        <!-- Back button -->
        <a href="{{ url_for('login') }}" class="back-btn glass-btn">
            <span class="back-icon">←</span>
        </a>
        
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-icon">👤</div>
            <h1 class="logo-text">Создать аккаунт</h1>
            <p class="logo-subtitle">Присоединяйтесь к нам</p>
        </div>
        
        <!-- Registration Form -->
        <form id="register-form" class="auth-form">
            <div class="form-group">
                <label for="username" class="form-label">Имя пользователя</label>
                <div class="input-container">
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="Введите имя пользователя"
                        required
                        minlength="3"
                        autocomplete="username"
                    >
                    <span class="input-icon">👤</span>
                </div>
                <div class="input-hint">Минимум 3 символа</div>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email</label>
                <div class="input-container">
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="Введите ваш email"
                        required
                        autocomplete="email"
                    >
                    <span class="input-icon">📧</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Пароль</label>
                <div class="input-container">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="Создайте надежный пароль"
                        required
                        minlength="6"
                        autocomplete="new-password"
                    >
                    <span class="input-icon">🔒</span>
                    <button type="button" class="password-toggle" id="password-toggle">
                        <span class="toggle-icon">👁️</span>
                    </button>
                </div>
                <div class="input-hint">Минимум 6 символов</div>
            </div>
            
            <div class="form-group">
                <label for="confirm-password" class="form-label">Подтвердите пароль</label>
                <div class="input-container">
                    <input 
                        type="password" 
                        id="confirm-password" 
                        name="confirm-password" 
                        class="form-input" 
                        placeholder="Повторите пароль"
                        required
                        autocomplete="new-password"
                    >
                    <span class="input-icon">🔒</span>
                    <button type="button" class="password-toggle" id="confirm-password-toggle">
                        <span class="toggle-icon">👁️</span>
                    </button>
                </div>
            </div>
            
            <div class="form-group">
                <label class="checkbox-container">
                    <input type="checkbox" id="accept-terms" name="terms" required>
                    <span class="checkmark"></span>
                    Я принимаю <a href="#" class="terms-link">условия использования</a>
                </label>
            </div>
            
            <button type="submit" class="btn btn-primary" id="register-btn">
                <span class="btn-text">Создать аккаунт</span>
                <div class="btn-loader hidden">
                    <div class="loader"></div>
                </div>
            </button>
            
            <div class="divider">
                <span>или</span>
            </div>
            
            <a href="{{ url_for('login') }}" class="btn btn-secondary">
                Уже есть аккаунт? Войти
            </a>
        </form>
    </div>
</div>

<!-- Terms Modal -->
<div id="terms-modal" class="modal hidden">
    <div class="modal-content glass-card">
        <div class="modal-header">
            <h2>Условия использования</h2>
            <button class="modal-close" id="close-terms">&times;</button>
        </div>
        <div class="modal-body">
            <p>Добро пожаловать в InfernoSpaceX!</p>
            <p>Используя наше приложение, вы соглашаетесь с следующими условиями:</p>
            <ul>
                <li>Вы обязуетесь использовать приложение только в законных целях</li>
                <li>Вы не будете пытаться взломать или нарушить безопасность системы</li>
                <li>Ваши данные будут защищены с помощью современных методов шифрования</li>
                <li>Мы не передаем ваши данные третьим лицам</li>
            </ul>
            <p>Нарушение этих условий может привести к блокировке аккаунта.</p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" id="accept-terms-btn">Принять</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeRegister();
});
</script>
{% endblock %}
