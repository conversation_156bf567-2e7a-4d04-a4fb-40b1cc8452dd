{% extends "base.html" %}

{% block title %}Вход - InfernoSpaceX{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card glass-card">
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-icon">🚀</div>
            <h1 class="logo-text">InfernoSpaceX</h1>
            <p class="logo-subtitle">Добро пожаловать</p>
        </div>
        
        <!-- Login Form -->
        <form id="login-form" class="auth-form">
            <div class="form-group">
                <label for="username" class="form-label">Email или имя пользователя</label>
                <div class="input-container">
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        placeholder="Введите ваш email"
                        required
                        autocomplete="username"
                    >
                    <span class="input-icon">👤</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Пароль</label>
                <div class="input-container">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        placeholder="Введите ваш пароль"
                        required
                        autocomplete="current-password"
                    >
                    <span class="input-icon">🔒</span>
                    <button type="button" class="password-toggle" id="password-toggle">
                        <span class="toggle-icon">👁️</span>
                    </button>
                </div>
            </div>
            
            <div class="form-options">
                <label class="checkbox-container">
                    <input type="checkbox" id="remember-me" name="remember">
                    <span class="checkmark"></span>
                    Запомнить меня
                </label>
                
                <a href="#" class="forgot-password">Забыли пароль?</a>
            </div>
            
            <button type="submit" class="btn btn-primary" id="login-btn">
                <span class="btn-text">Войти</span>
                <div class="btn-loader hidden">
                    <div class="loader"></div>
                </div>
            </button>
            
            <div class="divider">
                <span>или</span>
            </div>
            
            <a href="{{ url_for('register') }}" class="btn btn-secondary">
                Создать аккаунт
            </a>
        </form>
        
        <!-- Test credentials info -->
        <div class="test-info glass-card">
            <div class="info-icon">ℹ️</div>
            <div class="info-content">
                <p class="info-title">Тестовые данные:</p>
                <p class="info-text"><EMAIL> / 123456</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeLogin();
});
</script>
{% endblock %}
