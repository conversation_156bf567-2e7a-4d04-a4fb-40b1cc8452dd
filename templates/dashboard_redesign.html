{% extends "base.html" %}

{% block title %}InfernoSpaceX Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-redesign.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="dashboard-container theme-dark" id="dashboard-container">
    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-brand">
            <div class="brand-icon">
                <i class="fas fa-fire"></i>
            </div>
            <div class="brand-text">
                <h1>InfernoSpaceX</h1>
                <span>Control Panel</span>
            </div>
        </div>
        
        <div class="nav-user">
            <div class="user-avatar" id="nav-user-avatar">
                U
            </div>
            <div class="user-details">
                <span class="user-name" id="username">{{ username or 'User' }}</span>
                <span class="user-role" id="nav-user-role">User</span>
            </div>
            <button class="logout-btn" data-action="logout">
                <i class="fas fa-sign-out-alt"></i>
                Exit
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="tab-navigation">
                <button class="tab-btn active" data-tab="profile">
                    <i class="fas fa-user-circle"></i>
                    <span>Profile</span>
                </button>
                <button class="tab-btn" data-tab="tools">
                    <i class="fas fa-tools"></i>
                    <span>Tools</span>
                </button>
                <button class="tab-btn" data-tab="chat">
                    <i class="fas fa-comments"></i>
                    <span>Chat</span>
                </button>
                <button class="tab-btn" data-tab="security">
                    <i class="fas fa-shield-alt"></i>
                    <span>Security</span>
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </button>
            </div>
        </aside>

        <!-- Content Area -->
        <main class="content-area">
            <!-- Profile Tab -->
            <div class="tab-content active" id="profile-tab">
                <div class="content-header">
                    <h2><i class="fas fa-user-circle"></i> User Profile</h2>
                    <p>Manage your account information and preferences</p>
                </div>
                
                <div class="grid grid-cols-2">
                    <!-- Profile Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-id-card"></i> Account Information</h3>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <div class="profile-avatar-large" id="profile-avatar">
                                    U
                                </div>
                                <div class="profile-info">
                                    <h4 id="profile-username">{{ username or 'User' }}</h4>
                                    <div class="role-badge" id="profile-role">User</div>
                                </div>
                            </div>
                            
                            <div class="info-list">
                                <div class="info-item">
                                    <span class="info-label">Email:</span>
                                    <span class="info-value" id="user-email">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Member Since:</span>
                                    <span class="info-value" id="member-since">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Last Login:</span>
                                    <span class="info-value" id="last-login">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Hardware ID:</span>
                                    <span class="info-value" id="hardware-id">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Statistics -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> Account Statistics</h3>
                        </div>
                        <div class="card-content">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-shield-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value">Active</span>
                                        <span class="stat-label">Security Status</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="session-time">0m</span>
                                        <span class="stat-label">Session Time</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="user-ip">Loading...</span>
                                        <span class="stat-label">IP Address</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tools Tab -->
            <div class="tab-content" id="tools-tab">
                <div class="content-header">
                    <h2><i class="fas fa-tools"></i> Analysis Tools</h2>
                    <p>IP analysis and user lookup tools</p>
                </div>
                
                <div class="grid grid-cols-2">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-globe"></i> IP Analysis</h3>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label class="form-label">IP Address</label>
                                <input type="text" class="form-input" id="ip-input" placeholder="Enter IP address">
                            </div>
                            <button class="btn btn-primary" id="analyze-ip">
                                <i class="fas fa-search"></i>
                                Analyze
                            </button>
                            <div id="ip-results" class="results-area"></div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-user-search"></i> User Lookup</h3>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-input" id="user-input" placeholder="Enter username">
                            </div>
                            <button class="btn btn-primary" id="search-user">
                                <i class="fas fa-search"></i>
                                Search
                            </button>
                            <div id="user-results" class="results-area"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-content" id="chat-tab">
                <div class="content-header">
                    <h2><i class="fas fa-comments"></i> Chat</h2>
                    <p>Communicate with other users</p>
                </div>
                
                <div class="card">
                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            <!-- Messages will be loaded here -->
                        </div>
                        <div class="chat-input-area">
                            <div class="chat-input-group">
                                <input type="text" class="form-input" id="chat-input" placeholder="Type your message...">
                                <button class="btn btn-primary" id="send-message">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="tab-content" id="security-tab">
                <div class="content-header">
                    <h2><i class="fas fa-shield-alt"></i> Security Center</h2>
                    <p>Monitor security status and threats</p>
                </div>
                
                <div class="grid grid-cols-2">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-line"></i> Threat Statistics</h3>
                        </div>
                        <div class="card-content">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-ban"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="blocked-ips">0</span>
                                        <span class="stat-label">Blocked IPs</span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value" id="recent-threats">0</span>
                                        <span class="stat-label">Recent Threats</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-cogs"></i> Quick Actions</h3>
                        </div>
                        <div class="card-content">
                            <button class="btn btn-secondary full-width" onclick="window.open('/security-dashboard')">
                                <i class="fas fa-external-link-alt"></i>
                                Open Security Dashboard
                            </button>
                            <button class="btn btn-secondary full-width" id="refresh-stats">
                                <i class="fas fa-sync-alt"></i>
                                Refresh Statistics
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-content" id="settings-tab">
                <div class="content-header">
                    <h2><i class="fas fa-cog"></i> Settings</h2>
                    <p>Customize your experience</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-palette"></i> Appearance</h3>
                    </div>
                    <div class="card-content">
                        <div class="form-group">
                            <label class="form-label">Theme</label>
                            <select class="form-input" id="theme-selector">
                                <option value="dark">Dark</option>
                                <option value="light">Light</option>
                                <option value="auto">Auto</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" id="notifications-toggle"> 
                                Enable Notifications
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/dashboard-redesign.js') }}"></script>
{% endblock %}
