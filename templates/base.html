<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta name="referrer" content="no-referrer">
    <title>{% block title %}InfernoSpaceX{% endblock %}</title>
    
    <!-- Security headers via meta tags -->
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="{{ url_for('static', filename='css/main.css') }}" as="style">
    
    <!-- Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}

    <!-- Full screen override for dashboard -->
    <style>
        .fullscreen-override {
            margin: 0 !important;
            padding: 0 !important;
            max-width: none !important;
            width: 100vw !important;
            overflow-x: hidden !important;
        }

        .fullscreen-override .container,
        .fullscreen-override .main-container {
            max-width: none !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
</head>
<body>
    <!-- Background orbs for glassmorphism effect -->
    <div class="bg-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
    </div>
    
    <!-- Main content -->
    <main class="main-container">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Загрузка...</p>
        </div>
    </div>
    
    <!-- Toast notifications -->
    <div id="toast-container" class="toast-container"></div>
    
    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
