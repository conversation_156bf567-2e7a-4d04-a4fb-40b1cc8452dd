#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Supabase Tables Script
Automatically creates all necessary tables in Supabase
"""

from supabase import create_client, Client
import time

# Supabase configuration
SUPABASE_URL = "https://uceidograrzhuwobxdqc.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVjZWlkb2dyYXJ6aHV3b2J4ZHFjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MzAwNjMsImV4cCI6MjA2OTMwNjA2M30.v_5o2IKZSvwGGCBfcGdCLVTrQVZD4dvJcFbwDvx4Oac"

def create_tables():
    """Create all necessary tables in Supabase"""
    try:
        # Initialize Supabase client
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Connected to Supabase")
        
        # Test connection by trying to create a simple record
        print("🧪 Testing Supabase connection...")
        
        # Try to insert a test user
        test_result = supabase.table('users').insert({
            'username': 'test_user_' + str(int(time.time())),
            'email': f'test_{int(time.time())}@example.com',
            'password_hash': 'test_hash',
            'is_active': True,
            'role': 'user'
        }).execute()
        
        if test_result.data:
            print("✅ Supabase tables exist and working!")
            user_id = test_result.data[0]['id']
            
            # Clean up test user
            supabase.table('users').delete().eq('id', user_id).execute()
            print("🧹 Cleaned up test data")
            
            return True
        else:
            print("❌ Failed to insert test data")
            return False
            
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Supabase error: {error_msg}")
        
        if "relation" in error_msg.lower() and "does not exist" in error_msg.lower():
            print("\n🔧 Tables don't exist! Please create them in Supabase Dashboard:")
            print_table_creation_sql()
            return False
        elif "duplicate key" in error_msg.lower():
            print("✅ Tables exist (got duplicate key error, which is expected)")
            return True
        else:
            print(f"❌ Unknown error: {error_msg}")
            return False

def print_table_creation_sql():
    """Print SQL commands to create tables"""
    print("\n" + "="*80)
    print("📝 COPY AND PASTE THIS SQL INTO YOUR SUPABASE SQL EDITOR:")
    print("="*80)
    
    sql_commands = """
-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    role VARCHAR(20) DEFAULT 'user'
);

-- Security events table
CREATE TABLE IF NOT EXISTS security_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    severity VARCHAR(20) DEFAULT 'INFO',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Threat intelligence table
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id SERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    threat_type VARCHAR(50),
    threat_score INTEGER DEFAULT 0,
    country VARCHAR(2),
    is_malicious BOOLEAN DEFAULT FALSE,
    first_seen TIMESTAMP DEFAULT NOW(),
    last_seen TIMESTAMP DEFAULT NOW(),
    metadata JSONB
);

-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip_address);
CREATE INDEX IF NOT EXISTS idx_security_events_created ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_threat_intelligence_ip ON threat_intelligence(ip_address);
"""
    
    print(sql_commands)
    print("="*80)
    print("🔗 Go to: https://uceidograrzhuwobxdqc.supabase.co/project/uceidograrzhuwobxdqc/sql")
    print("📋 Paste the SQL above and click 'Run'")
    print("="*80)

def test_basic_operations():
    """Test basic database operations"""
    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        print("\n🧪 Testing basic operations...")
        
        # Test 1: Create user
        test_username = f"testuser_{int(time.time())}"
        result = supabase.table('users').insert({
            'username': test_username,
            'email': f'{test_username}@example.com',
            'password_hash': 'test_hash_123',
            'is_active': True,
            'role': 'user'
        }).execute()
        
        if result.data:
            user_id = result.data[0]['id']
            print(f"✅ Created test user: {test_username} (ID: {user_id})")
            
            # Test 2: Read user
            read_result = supabase.table('users').select('*').eq('id', user_id).execute()
            if read_result.data:
                print("✅ Successfully read user data")
            
            # Test 3: Log security event
            event_result = supabase.table('security_events').insert({
                'event_type': 'test_event',
                'user_id': user_id,
                'ip_address': '127.0.0.1',
                'user_agent': 'Test Agent',
                'details': {'test': True},
                'severity': 'INFO'
            }).execute()
            
            if event_result.data:
                print("✅ Successfully logged security event")
            
            # Cleanup
            supabase.table('security_events').delete().eq('user_id', user_id).execute()
            supabase.table('users').delete().eq('id', user_id).execute()
            print("🧹 Cleaned up test data")
            
            return True
        else:
            print("❌ Failed to create test user")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 InfernoSpaceX Supabase Setup")
    print("="*50)
    
    success = create_tables()
    
    if success:
        print("\n✅ Supabase is ready!")
        
        # Run additional tests
        if test_basic_operations():
            print("\n🎉 All tests passed! Supabase is fully functional!")
            print("\n🚀 You can now run: python app.py")
        else:
            print("\n⚠️  Basic operations failed. Check your Supabase configuration.")
    else:
        print("\n❌ Supabase setup failed. Please create the tables manually.")
        print("\n💡 After creating tables, run this script again to test.")
